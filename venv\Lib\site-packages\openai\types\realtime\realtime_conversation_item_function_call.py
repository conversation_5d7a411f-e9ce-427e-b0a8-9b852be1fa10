# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import Optional
from typing_extensions import Literal

from ..._models import BaseModel

__all__ = ["RealtimeConversationItemFunctionCall"]


class RealtimeConversationItemFunctionCall(BaseModel):
    arguments: str
    """The arguments of the function call.

    This is a JSON-encoded string representing the arguments passed to the function,
    for example `{"arg1": "value1", "arg2": 42}`.
    """

    name: str
    """The name of the function being called."""

    type: Literal["function_call"]
    """The type of the item. Always `function_call`."""

    id: Optional[str] = None
    """The unique ID of the item.

    This may be provided by the client or generated by the server.
    """

    call_id: Optional[str] = None
    """The ID of the function call."""

    object: Optional[Literal["realtime.item"]] = None
    """Identifier for the API object being returned - always `realtime.item`.

    Optional when creating a new item.
    """

    status: Optional[Literal["completed", "incomplete", "in_progress"]] = None
    """The status of the item. Has no effect on the conversation."""
