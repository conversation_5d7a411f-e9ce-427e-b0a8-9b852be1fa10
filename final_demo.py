"""
网页爬取和关键词提取服务 - 完整演示
展示从网页爬取到关键词提取的完整流程
"""

from model import GeoExpert
from config import config
from web_crawler import SafeWebCrawler

def crawl_and_analyze(url):
    """
    爬取网页并分析关键词
    :param url: 要爬取的网址
    :return: 分析结果
    """
    print(f"🕷️ 开始爬取网站: {url}")
    
    # 创建爬虫和分析器实例
    crawler = SafeWebCrawler(config)
    geo_expert = GeoExpert(config)
    
    # 爬取网页
    crawled_text = crawler.crawl(url)
    
    if not crawled_text:
        return {
            "success": False,
            "message": "没有爬取到任何内容",
            "url": url
        }
    
    # 合并所有页面的文本内容
    combined_text = ""
    for url_key, content in crawled_text.items():
        combined_text += f"URL: {url_key}\n内容: {content}\n\n"
    
    print(f"✅ 成功爬取 {len(crawled_text)} 个页面")
    print(f"📄 总文本长度: {len(combined_text)} 字符")
    
    # 提取关键词
    print("🤖 正在分析内容并提取关键词...")
    keywords = geo_expert.keyword_extract(combined_text)
    
    return {
        "success": True,
        "url": url,
        "pages_crawled": len(crawled_text),
        "total_text_length": len(combined_text),
        "keywords": keywords,
        "crawled_urls": list(crawled_text.keys())
    }

def demo_multiple_sites():
    """演示多个网站的爬取和分析"""
    
    # 测试网站列表
    test_sites = [
        "https://example.com",
        "https://httpbin.org/html",  # 可能被robots.txt阻止
        "https://www.python.org",   # 可能内容较多
    ]
    
    print("🌐 网页爬取和关键词提取服务演示")
    print("=" * 60)
    
    results = []
    
    for i, url in enumerate(test_sites, 1):
        print(f"\n📍 测试 {i}/{len(test_sites)}: {url}")
        print("-" * 40)
        
        try:
            result = crawl_and_analyze(url)
            results.append(result)
            
            if result["success"]:
                print(f"✅ 分析完成!")
                print(f"📊 爬取页面数: {result['pages_crawled']}")
                print(f"📝 文本长度: {result['total_text_length']} 字符")
                print(f"🔍 关键词分析:")
                print(result["keywords"])
            else:
                print(f"❌ 失败: {result['message']}")
                
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            results.append({
                "success": False,
                "url": url,
                "error": str(e)
            })
    
    # 总结
    print("\n" + "=" * 60)
    print("📈 演示总结")
    print("=" * 60)
    
    successful = sum(1 for r in results if r.get("success", False))
    total = len(results)
    
    print(f"总测试网站: {total}")
    print(f"成功处理: {successful}")
    print(f"成功率: {successful/total*100:.1f}%")
    
    for result in results:
        status = "✅" if result.get("success", False) else "❌"
        print(f"{status} {result['url']}")

if __name__ == "__main__":
    demo_multiple_sites()
