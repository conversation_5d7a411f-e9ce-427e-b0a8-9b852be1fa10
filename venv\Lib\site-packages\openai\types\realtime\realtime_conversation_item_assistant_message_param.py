# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Iterable
from typing_extensions import Literal, Required, TypedDict

__all__ = ["RealtimeConversationItemAssistantMessageParam", "Content"]


class Content(TypedDict, total=False):
    audio: str
    """
    Base64-encoded audio bytes, these will be parsed as the format specified in the
    session output audio type configuration. This defaults to PCM 16-bit 24kHz mono
    if not specified.
    """

    text: str
    """The text content."""

    transcript: str
    """
    The transcript of the audio content, this will always be present if the output
    type is `audio`.
    """

    type: Literal["output_text", "output_audio"]
    """
    The content type, `output_text` or `output_audio` depending on the session
    `output_modalities` configuration.
    """


class RealtimeConversationItemAssistantMessageParam(TypedDict, total=False):
    content: Required[Iterable[Content]]
    """The content of the message."""

    role: Required[Literal["assistant"]]
    """The role of the message sender. Always `assistant`."""

    type: Required[Literal["message"]]
    """The type of the item. Always `message`."""

    id: str
    """The unique ID of the item.

    This may be provided by the client or generated by the server.
    """

    object: Literal["realtime.item"]
    """Identifier for the API object being returned - always `realtime.item`.

    Optional when creating a new item.
    """

    status: Literal["completed", "incomplete", "in_progress"]
    """The status of the item. Has no effect on the conversation."""
