import requests
from bs4 import BeautifulSoup
from urllib.robotparser import RobotFileParser
from urllib.parse import urlparse, urljoin
import time
import random
from user_agents import parse
import logging
from typing import Set, List, Optional
from config import config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("crawler.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SafeWebCrawler:
    def __init__(
        self,
        config,
        user_agents_list: Optional[List[str]] = None
    ):
        """
        初始化安全网页爬虫
        
        :param start_url: 起始URL
        :param max_depth: 最大爬取深度
        :param max_requests: 最大请求数量
        :param requests_per_second: 每秒最大请求数
        :param user_agents_list: 自定义User-Agent列表
        """
        self.max_depth = config.WebCrawlerConfig.max_depth
        self.max_requests = config.WebCrawlerConfig.max_requests
        self.requests_per_second = config.WebCrawlerConfig.max_requests_per_second
        self.delay_between_requests = 1.0 / self.requests_per_second if self.requests_per_second > 0 else 0
        
        # 初始化User-Agent列表
        self.user_agents = user_agents_list if user_agents_list else [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (X11; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59"
        ]
        
        # 存储已爬取的URL
        self.crawled_urls: Set[str] = set()

        # 存储待爬取的URL及其深度
        self.to_crawl: List[tuple] = []

        # 存储爬取的文本内容
        self.crawled_text: dict = {}
        
        # 初始化robots.txt解析器
        self.robot_parser = RobotFileParser()
        try:
            self.robot_parser.read()
        except Exception as e:
            logger.warning(f"无法读取robots.txt: {e}")
    
    def is_allowed(self, url: str) -> bool:
        """检查是否允许爬取该URL"""
        # 检查是否在同一域名下
        parsed_url = urlparse(url)
        if parsed_url.netloc != self.base_domain:
            logger.info(f"跳过不同域名的URL: {url}")
            return False
            
        # 检查robots.txt规则
        if not self.robot_parser.can_fetch("*", url):
            logger.info(f"根据robots.txt，不允许爬取: {url}")
            return False
            
        # 检查是否已爬取
        if url in self.crawled_urls:
            return False
            
        return True
    
    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        return random.choice(self.user_agents)
    
    def fetch_page(self, url: str) -> Optional[str]:
        """获取网页内容"""
        try:
            headers = {
                "User-Agent": self.get_random_user_agent(),
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1"
            }
            
            response = requests.get(
                url,
                headers=headers,
                timeout=10,
                allow_redirects=True
            )
            
            # 检查响应状态码
            if response.status_code != 200:
                logger.warning(f"请求失败，状态码: {response.status_code}，URL: {url}")
                return None
                
            # 检查内容类型是否为HTML
            content_type = response.headers.get('Content-Type', '')
            if 'text/html' not in content_type:
                logger.info(f"跳过非HTML内容，URL: {url}，类型: {content_type}")
                return None
                
            return response.text
            
        except Exception as e:
            logger.error(f"获取页面时出错 {url}: {str(e)}")
            return None
    
    def extract_links(self, html: str, base_url: str) -> List[str]:
        """从HTML中提取链接"""
        soup = BeautifulSoup(html, 'html.parser')
        links = []
        
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href']
            # 拼接完整URL
            full_url = urljoin(base_url, href)
            # 规范化URL（移除锚点）
            parsed_url = urlparse(full_url)
            normalized_url = parsed_url._replace(fragment='').geturl()
            links.append(normalized_url)
            
        return list(set(links))  # 去重
    
    def extract_text(self, html: str) -> str:
        """从HTML中提取文本内容"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # 移除脚本和样式
        for script in soup(["script", "style"]):
            script.decompose()
        
        # 获取文本并清理
        text = soup.get_text()
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = '\n'.join(chunk for chunk in chunks if chunk)
        
        return text
    
    def crawl(self, startUrl: str) -> dict:
        """执行爬取过程"""
        logger.info(f"开始爬取，起始URL: {startUrl}")
        logger.info(f"配置: 最大深度={self.max_depth}, 最大请求数={self.max_requests}, 每秒请求数={self.requests_per_second}")
        
        last_request_time = 0.0
        self.base_domain = urlparse(startUrl).netloc # 爬取的域名，用于检查是否允许爬取的规则，可以是主域名或子域名，如example.com或www.example.com。
        robots_url = urljoin(startUrl, "/robots.txt") # 读取robots.txt文件
        self.robot_parser.set_url(robots_url) # 设置robots.txt文件的URL
        self.robot_parser.read()    # 读取robots.txt文件
        logger.info(f"成功读取robots.txt: {robots_url}") # 打印robots.txt文件的URL
        self.to_crawl.append((startUrl, 0)) # 将起始URL添加到待爬取的URL列表中

        while self.to_crawl and len(self.crawled_urls) < self.max_requests:
            # 控制请求频率
            current_time = time.time()
            time_since_last = current_time - last_request_time
            if time_since_last < self.delay_between_requests:
                sleep_time = self.delay_between_requests - time_since_last
                time.sleep(sleep_time)
            
            url, depth = self.to_crawl.pop(0)
            
            # 检查是否允许爬取
            if not self.is_allowed(url):
                continue
            
            logger.info(f"爬取: {url} (深度: {depth})")
            
            # 获取页面内容
            html = self.fetch_page(url)
            last_request_time = time.time()
            
            if not html:
                continue
            
            # 提取文本并存储
            text = self.extract_text(html)
            self.crawled_text[url] = text
            self.crawled_urls.add(url)
            
            # 如果还没到最大深度，提取链接继续爬取
            if depth < self.max_depth:
                links = self.extract_links(html, url)
                new_links_count = 0
                
                for link in links:
                    if self.is_allowed(link) and (link, depth + 1) not in self.to_crawl:
                        self.to_crawl.append((link, depth + 1))
                        new_links_count += 1
                
                logger.info(f"从 {url} 发现 {new_links_count} 个新链接")
        
        logger.info(f"爬取完成，共爬取 {len(self.crawled_urls)} 个页面")
        return self.crawled_text
    
    def save_to_files(self, output_dir: str = "crawled_texts"):
        """将爬取的文本保存到文件"""
        import os
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        for url, text in self.crawled_text.items():
            # 生成文件名
            parsed_url = urlparse(url)
            filename = f"{parsed_url.netloc}{parsed_url.path}".replace("/", "_")
            if not filename:
                filename = "index"
            filename = f"{filename}.txt"
            
            file_path = os.path.join(output_dir, filename)
            
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"URL: {url}\n\n")
                    f.write(text)
                logger.info(f"已保存: {file_path}")
            except Exception as e:
                logger.error(f"保存文件 {file_path} 失败: {e}")

if __name__ == "__main__":
    # 示例用法
    import argparse
    
    parser = argparse.ArgumentParser(description='安全网页爬虫')
    parser.add_argument('--depth', type=int, default=2, help='最大爬取深度')
    parser.add_argument('--max-requests', type=int, default=50, help='最大请求数量')
    parser.add_argument('--requests-per-second', type=float, default=1.0, help='每秒最大请求数')
    
    args = parser.parse_args()
    
    # 创建并运行爬虫
    crawler = SafeWebCrawler(
        max_depth=args.depth,
        max_requests=args.max_requests,
        requests_per_second=args.requests_per_second
    )
    sample_url = "https://www.sling-stones.com"
    results = crawler.crawl(sample_url)
    
    # 保存结果
    crawler.save_to_files()
