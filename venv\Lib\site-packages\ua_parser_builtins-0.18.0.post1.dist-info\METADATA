Metadata-Version: 2.3
Name: ua-parser-builtins
Version: 0.18.0.post1
Summary: Precompiled rules for User Agent Parser
Project-URL: repository, https://github.com/ua-parser/uap-python
Maintainer-email: masklinn <<EMAIL>>
License: Apache 2.0
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Internet :: WWW/HTTP
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.9
Description-Content-Type: text/markdown

# Precompiled ruleset for [ua-parser](https://pypi.org/project/ua-parser/)

This project does not do anything on its own, nor does it have any
actual API: it contains the dataset of
[uap-core](https://github.com/ua-parser/uap-core) pre-compiled for use
by [ua-parser](https://pypi.org/project/ua-parser/) to decrease
initialisation times.
