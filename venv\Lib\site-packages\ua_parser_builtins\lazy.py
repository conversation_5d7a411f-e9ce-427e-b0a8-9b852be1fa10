########################################################
# NOTICE: this file is autogenerated from regexes.yaml #
########################################################
__all__ = ["MATCHERS"]

from typing import Tuple, List
from ua_parser.lazy import UserAgentMatcher, OSMatcher, DeviceMatcher

MATCHERS: Tuple[List[UserAgentMatcher], List[OSMatcher], List[DeviceMatcher]] = ([
    UserAgentMatcher('(GeoEvent Server) (\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)'),
    UserAgentMatcher('(ArcGIS Pro)(?: (\\d+)\\.(\\d+)\\.([^ ]+)|)'),
    UserAgentMatcher('ArcGIS Client Using WinInet', 'ArcMap'),
    UserAgentMatcher('(OperationsDashboard)-(?:Windows)-(\\d+)\\.(\\d+)\\.(\\d+)', 'Operations Dashboard for ArcGIS'),
    UserAgentMatcher('(arcgisearth)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'ArcGIS Earth'),
    UserAgentMatcher('com.esri.(earth).phone/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'ArcGIS Earth'),
    UserAgentMatcher('(arcgis-explorer)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Explorer for ArcGIS'),
    UserAgentMatcher('arcgis-(collector|aurora)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Collector for ArcGIS'),
    UserAgentMatcher('(arcgis-workforce)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Workforce for ArcGIS'),
    UserAgentMatcher('(Collector|Explorer|Workforce)-(?:Android|iOS)-(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', '$1 for ArcGIS'),
    UserAgentMatcher('(Explorer|Collector)/(\\d+) CFNetwork', '$1 for ArcGIS'),
    UserAgentMatcher('ArcGISRuntime-(Android|iOS|NET|Qt)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'ArcGIS Runtime SDK for $1'),
    UserAgentMatcher('ArcGIS\\.?(iOS|Android|NET|Qt)(?:-|\\.)(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'ArcGIS Runtime SDK for $1'),
    UserAgentMatcher('ArcGIS\\.Runtime\\.(Qt)\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'ArcGIS Runtime SDK for $1'),
    UserAgentMatcher('^(Luminary)[Stage]+/(\\d+) CFNetwork'),
    UserAgentMatcher('(ESPN)[%20| ]+Radio/(\\d+)\\.(\\d+)\\.(\\d+) CFNetwork'),
    UserAgentMatcher('(Antenna)/(\\d+) CFNetwork', 'AntennaPod'),
    UserAgentMatcher('(TopPodcasts)Pro/(\\d+) CFNetwork'),
    UserAgentMatcher('(MusicDownloader)Lite/(\\d+)\\.(\\d+)\\.(\\d+) CFNetwork'),
    UserAgentMatcher('^(.{0,200})-iPad\\/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)(?:\\.(\\d+)|) CFNetwork'),
    UserAgentMatcher('^(.{0,200})-iPhone/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)(?:\\.(\\d+)|) CFNetwork'),
    UserAgentMatcher('^(.{0,200})/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)(?:\\.(\\d+)|) CFNetwork'),
    UserAgentMatcher('^(Luminary)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(espn\\.go)', 'ESPN'),
    UserAgentMatcher('(espnradio\\.com)', 'ESPN'),
    UserAgentMatcher('ESPN APP$', 'ESPN'),
    UserAgentMatcher('(audioboom\\.com)', 'AudioBoom'),
    UserAgentMatcher(' (Rivo) RHYTHM'),
    UserAgentMatcher('(CFNetwork)(?:/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)', 'CFNetwork'),
    UserAgentMatcher('(Pingdom\\.com_bot_version_)(\\d+)\\.(\\d+)', 'PingdomBot'),
    UserAgentMatcher('(PingdomTMS)/(\\d+)\\.(\\d+)\\.(\\d+)', 'PingdomBot'),
    UserAgentMatcher('(PingdomPageSpeed)/(\\d+)\\.(\\d+)', 'PingdomBot'),
    UserAgentMatcher(' (PTST)/(\\d+)(?:\\.(\\d+)|)$', 'WebPageTest.org bot'),
    UserAgentMatcher('X11; (Datanyze); Linux'),
    UserAgentMatcher('(NewRelicPinger)/(\\d+)\\.(\\d+)', 'NewRelicPingerBot'),
    UserAgentMatcher('(Tableau)/(\\d+)\\.(\\d+)', 'Tableau'),
    UserAgentMatcher('AppleWebKit/\\d{1,10}\\.\\d{1,10}.{0,200} Safari.{0,200} (CreativeCloud)/(\\d+)\\.(\\d+).(\\d+)', 'Adobe CreativeCloud'),
    UserAgentMatcher('(Salesforce)(?:.)\\/(\\d+)\\.(\\d?)'),
    UserAgentMatcher('(\\(StatusCake\\))', 'StatusCakeBot'),
    UserAgentMatcher('(facebookexternalhit)/(\\d+)\\.(\\d+)', 'FacebookBot'),
    UserAgentMatcher('Google.{0,50}/\\+/web/snippet', 'GooglePlusBot'),
    UserAgentMatcher('via ggpht\\.com GoogleImageProxy', 'GmailImageProxy'),
    UserAgentMatcher('YahooMailProxy; https://help\\.yahoo\\.com/kb/yahoo-mail-proxy-SLN28749\\.html', 'YahooMailProxy'),
    UserAgentMatcher('(Twitterbot)/(\\d+)\\.(\\d+)', 'Twitterbot'),
    UserAgentMatcher('/((?:Ant-|)Nutch|[A-z]+[Bb]ot|[A-z]+[Ss]pider|Axtaris|fetchurl|Isara|ShopSalad|Tailsweep)[ \\-](\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)'),
    UserAgentMatcher('\\b(008|Altresium|Argus|BaiduMobaider|BoardReader|DNSGroup|DataparkSearch|EDI|Goodzer|Grub|INGRID|Infohelfer|LinkedInBot|LOOQ|Nutch|OgScrper|Pandora|PathDefender|Peew|PostPost|Steeler|Twitterbot|VSE|WebCrunch|WebZIP|Y!J-BR[A-Z]|YahooSeeker|envolk|sproose|wminer)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(MSIE) (\\d+)\\.(\\d+)([a-z]\\d|[a-z]|);.{0,200} MSIECrawler', 'MSIECrawler'),
    UserAgentMatcher('(DAVdroid)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(Google-HTTP-Java-Client|Apache-HttpClient|PostmanRuntime|Go-http-client|scalaj-http|http%20client|Python-urllib|HttpMonitor|TLSProber|WinHTTP|JNLP|okhttp|aihttp|reqwest|axios|unirest-(?:java|python|ruby|nodejs|php|net))(?:[ /](\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)'),
    UserAgentMatcher('(Pinterest(?:bot|))/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)[;\\s(]+\\+https://www.pinterest.com/bot.html', 'Pinterestbot'),
    UserAgentMatcher('(CSimpleSpider|Cityreview Robot|CrawlDaddy|CrawlFire|Finderbots|Index crawler|Job Roboter|KiwiStatus Spider|Lijit Crawler|QuerySeekerSpider|ScollSpider|Trends Crawler|USyd-NLP-Spider|SiteCat Webbot|BotName\\/\\$BotVersion|123metaspider-Bot|1470\\.net crawler|50\\.nu|8bo Crawler Bot|Aboundex|Accoona-[A-z]{1,30}-Agent|AdsBot-Google(?:-[a-z]{1,30}|)|altavista|AppEngine-Google|archive.{0,30}\\.org_bot|archiver|Ask Jeeves|[Bb]ai[Dd]u[Ss]pider(?:-[A-Za-z]{1,30})(?:-[A-Za-z]{1,30}|)|bingbot|BingPreview|blitzbot|BlogBridge|Bloglovin|BoardReader Blog Indexer|BoardReader Favicon Fetcher|boitho.com-dc|BotSeer|BUbiNG|\\b\\w{0,30}favicon\\w{0,30}\\b|\\bYeti(?:-[a-z]{1,30}|)|Catchpoint(?: bot|)|[Cc]harlotte|Checklinks|clumboot|Comodo HTTP\\(S\\) Crawler|Comodo-Webinspector-Crawler|ConveraCrawler|CRAWL-E|CrawlConvera|Daumoa(?:-feedfetcher|)|Feed Seeker Bot|Feedbin|findlinks|Flamingo_SearchEngine|FollowSite Bot|furlbot|Genieo|gigabot|GomezAgent|gonzo1|(?:[a-zA-Z]{1,30}-|)Googlebot(?:-[a-zA-Z]{1,30}|)|Google SketchUp|grub-client|gsa-crawler|heritrix|HiddenMarket|holmes|HooWWWer|htdig|ia_archiver|ICC-Crawler|Icarus6j|ichiro(?:/mobile|)|IconSurf|IlTrovatore(?:-Setaccio|)|InfuzApp|Innovazion Crawler|InternetArchive|IP2[a-z]{1,30}Bot|jbot\\b|KaloogaBot|Kraken|Kurzor|larbin|LEIA|LesnikBot|Linguee Bot|LinkAider|LinkedInBot|Lite Bot|Llaut|lycos|Mail\\.RU_Bot|masscan|masidani_bot|Mediapartners-Google|Microsoft .{0,30} Bot|mogimogi|mozDex|MJ12bot|msnbot(?:-media {0,2}|)|msrbot|Mtps Feed Aggregation System|netresearch|Netvibes|NewsGator[^/]{0,30}|^NING|Nutch[^/]{0,30}|Nymesis|ObjectsSearch|OgScrper|Orbiter|OOZBOT|PagePeeker|PagesInventory|PaxleFramework|Peeplo Screenshot Bot|PHPCrawl|PlantyNet_WebRobot|Pompos|Qwantify|Read%20Later|Reaper|RedCarpet|Retreiver|Riddler|Rival IQ|scooter|Scrapy|Scrubby|searchsight|seekbot|semanticdiscovery|SemrushBot|Simpy|SimplePie|SEOstats|SimpleRSS|SiteCon|Slackbot-LinkExpanding|Slack-ImgProxy|Slurp|snappy|Speedy Spider|Squrl Java|Stringer|TheUsefulbot|ThumbShotsBot|Thumbshots\\.ru|Tiny Tiny RSS|Twitterbot|WhatsApp|URL2PNG|Vagabondo|VoilaBot|^vortex|Votay bot|^voyager|WASALive.Bot|Web-sniffer|WebThumb|WeSEE:[A-z]{1,30}|WhatWeb|WIRE|WordPress|Wotbox|www\\.almaden\\.ibm\\.com|Xenu(?:.s|) Link Sleuth|Xerka [A-z]{1,30}Bot|yacy(?:bot|)|YahooSeeker|Yahoo! Slurp|Yandex\\w{1,30}|YodaoBot(?:-[A-z]{1,30}|)|YottaaMonitor|Yowedo|^Zao|^Zao-Crawler|ZeBot_www\\.ze\\.bz|ZooShot|ZyBorg|ArcGIS Hub Indexer)(?:[ /]v?(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)|)'),
    UserAgentMatcher('\\b(Boto3?|JetS3t|aws-(?:cli|sdk-(?:cpp|go|java|nodejs|ruby2?|dotnet-(?:\\d{1,2}|core)))|s3fs)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(FME)\\/(\\d+\\.\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(QGIS)\\/(\\d)\\.?0?(\\d{1,2})\\.?0?(\\d{1,2})'),
    UserAgentMatcher('(JOSM)/(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Tygron Platform) \\((\\d+)\\.(\\d+)\\.(\\d+(?:\\.\\d+| RC \\d+\\.\\d+))'),
    UserAgentMatcher('\\[(FBAN/MessengerForiOS|FB_IAB/MESSENGER);FBAV/(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)|)', 'Facebook Messenger'),
    UserAgentMatcher('\\[FB.{0,300};(FBAV)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)', 'Facebook'),
    UserAgentMatcher('\\[FB.{0,300};', 'Facebook'),
    UserAgentMatcher('^.{0,200}?(?:\\/[A-Za-z0-9\\.]{0,50}|) {0,2}([A-Za-z0-9 \\-_\\!\\[\\]:]{0,50}(?:[Aa]rchiver|[Ii]ndexer|[Ss]craper|[Bb]ot|[Ss]pider|[Cc]rawl[a-z]{0,50}))[/ ](\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)'),
    UserAgentMatcher('^.{0,200}?((?:[A-Za-z][A-Za-z0-9 -]{0,50}|)[^C][^Uu][Bb]ot)\\b(?:(?:[ /]| v)(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)'),
    UserAgentMatcher('^.{0,200}?((?:[A-z0-9]{1,50}|[A-z\\-]{1,50} ?|)(?: the |)(?:[Ss][Pp][Ii][Dd][Ee][Rr]|[Ss]crape|[Cc][Rr][Aa][Ww][Ll])[A-z0-9]{0,50})(?:(?:[ /]| v)(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)'),
    UserAgentMatcher('(HbbTV)/(\\d+)\\.(\\d+)\\.(\\d+) \\('),
    UserAgentMatcher('(Chimera|SeaMonkey|Camino|Waterfox)/(\\d+)\\.(\\d+)\\.?([ab]?\\d+[a-z]*|)'),
    UserAgentMatcher('(SailfishBrowser)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'Sailfish Browser'),
    UserAgentMatcher('\\[(Pinterest)/[^\\]]{1,50}\\]'),
    UserAgentMatcher('(Pinterest)(?: for Android(?: Tablet|)|)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)'),
    UserAgentMatcher('Mozilla.{1,200}Mobile.{1,100}(Instagram).(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('Mozilla.{1,200}Mobile.{1,100}(Flipboard).(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('Mozilla.{1,200}Mobile.{1,100}(Flipboard-Briefing).(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('Mozilla.{1,200}Mobile.{1,100}(Onefootball)\\/Android.(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Snapchat)\\/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Twitter for (?:iPhone|iPad)|TwitterAndroid)(?:\\/(\\d+)\\.(\\d+)|)', 'Twitter'),
    UserAgentMatcher('Mozilla.{1,200}Mobile.{1,100}(Phantom\\/ios|android).(\\d+)\\.(\\d+)\\.(\\d+)', 'Phantom'),
    UserAgentMatcher('Mozilla.{1,100}Mobile.{1,100}(AspiegelBot|PetalBot)', 'Spider'),
    UserAgentMatcher('AspiegelBot|PetalBot', 'Spider'),
    UserAgentMatcher('(Firefox)/(\\d+)\\.(\\d+) Basilisk/(\\d+)', 'Basilisk'),
    UserAgentMatcher('(PaleMoon)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'Pale Moon'),
    UserAgentMatcher('(Fennec)/(\\d+)\\.(\\d+)\\.?([ab]?\\d+[a-z]*)', 'Firefox Mobile'),
    UserAgentMatcher('(Fennec)/(\\d+)\\.(\\d+)(pre)', 'Firefox Mobile'),
    UserAgentMatcher('(Fennec)/(\\d+)\\.(\\d+)', 'Firefox Mobile'),
    UserAgentMatcher('(?:Mobile|Tablet);.{0,200}(Firefox)/(\\d+)\\.(\\d+)', 'Firefox Mobile'),
    UserAgentMatcher('(Namoroka|Shiretoko|Minefield)/(\\d+)\\.(\\d+)\\.(\\d+(?:pre|))', 'Firefox ($1)'),
    UserAgentMatcher('(Firefox)/(\\d+)\\.(\\d+)(a\\d+[a-z]*)', 'Firefox Alpha'),
    UserAgentMatcher('(Firefox)/(\\d+)\\.(\\d+)(b\\d+[a-z]*)', 'Firefox Beta'),
    UserAgentMatcher('(Firefox)-(?:\\d+\\.\\d+|)/(\\d+)\\.(\\d+)(a\\d+[a-z]*)', 'Firefox Alpha'),
    UserAgentMatcher('(Firefox)-(?:\\d+\\.\\d+|)/(\\d+)\\.(\\d+)(b\\d+[a-z]*)', 'Firefox Beta'),
    UserAgentMatcher('(Namoroka|Shiretoko|Minefield)/(\\d+)\\.(\\d+)([ab]\\d+[a-z]*|)', 'Firefox ($1)'),
    UserAgentMatcher('(Firefox).{0,200}Tablet browser (\\d+)\\.(\\d+)\\.(\\d+)', 'MicroB'),
    UserAgentMatcher('(MozillaDeveloperPreview)/(\\d+)\\.(\\d+)([ab]\\d+[a-z]*|)'),
    UserAgentMatcher('(FxiOS)/(\\d+)\\.(\\d+)(\\.(\\d+)|)(\\.(\\d+)|)', 'Firefox iOS'),
    UserAgentMatcher('(Flock)/(\\d+)\\.(\\d+)(b\\d+?)'),
    UserAgentMatcher('(RockMelt)/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Navigator)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Netscape'),
    UserAgentMatcher('(Navigator)/(\\d+)\\.(\\d+)([ab]\\d+)', 'Netscape'),
    UserAgentMatcher('(Netscape6)/(\\d+)\\.(\\d+)\\.?([ab]?\\d+|)', 'Netscape'),
    UserAgentMatcher('(MyIBrow)/(\\d+)\\.(\\d+)', 'My Internet Browser'),
    UserAgentMatcher('(UC? ?Browser|UCWEB|U3)[ /]?(\\d+)\\.(\\d+)\\.(\\d+)', 'UC Browser'),
    UserAgentMatcher('(Opera Tablet).{0,200}Version/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(Opera Mini)(?:/att|)/?(\\d+|)(?:\\.(\\d+)|)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(Opera)/.{1,100}Opera Mobi.{1,100}Version/(\\d+)\\.(\\d+)', 'Opera Mobile'),
    UserAgentMatcher('(Opera)/(\\d+)\\.(\\d+).{1,100}Opera Mobi', 'Opera Mobile'),
    UserAgentMatcher('Opera Mobi.{1,100}(Opera)(?:/|\\s+)(\\d+)\\.(\\d+)', 'Opera Mobile'),
    UserAgentMatcher('Opera Mobi', 'Opera Mobile'),
    UserAgentMatcher('(Opera)/9.80.{0,200}Version/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(?:Mobile Safari).{1,300}(OPR)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Opera Mobile'),
    UserAgentMatcher('(?:Chrome).{1,300}(OPR)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Opera'),
    UserAgentMatcher('(Coast)/(\\d+).(\\d+).(\\d+)', 'Opera Coast'),
    UserAgentMatcher('(OPiOS)/(\\d+).(\\d+).(\\d+)', 'Opera Mini'),
    UserAgentMatcher('Chrome/.{1,200}( MMS)/(\\d+).(\\d+).(\\d+)', 'Opera Neon'),
    UserAgentMatcher('(hpw|web)OS/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'webOS Browser'),
    UserAgentMatcher('(luakit)', 'LuaKit'),
    UserAgentMatcher('(Snowshoe)/(\\d+)\\.(\\d+).(\\d+)'),
    UserAgentMatcher('Gecko/\\d+ (Lightning)/(\\d+)\\.(\\d+)\\.?((?:[ab]?\\d+[a-z]*)|(?:\\d*))'),
    UserAgentMatcher('(Firefox)/(\\d+)\\.(\\d+)\\.(\\d+(?:pre|)) \\(Swiftfox\\)', 'Swiftfox'),
    UserAgentMatcher('(Firefox)/(\\d+)\\.(\\d+)([ab]\\d+[a-z]*|) \\(Swiftfox\\)', 'Swiftfox'),
    UserAgentMatcher('(rekonq)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|) Safari', 'Rekonq'),
    UserAgentMatcher('rekonq', 'Rekonq'),
    UserAgentMatcher('(conkeror|Conkeror)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'Conkeror'),
    UserAgentMatcher('(konqueror)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Konqueror'),
    UserAgentMatcher('(WeTab)-Browser'),
    UserAgentMatcher('(Comodo_Dragon)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Comodo Dragon'),
    UserAgentMatcher('(Symphony) (\\d+).(\\d+)'),
    UserAgentMatcher('PLAYSTATION 3.{1,200}WebKit', 'NetFront NX'),
    UserAgentMatcher('PLAYSTATION 3', 'NetFront'),
    UserAgentMatcher('(PlayStation Portable)', 'NetFront'),
    UserAgentMatcher('(PlayStation Vita)', 'NetFront NX'),
    UserAgentMatcher('AppleWebKit.{1,200} (NX)/(\\d+)\\.(\\d+)\\.(\\d+)', 'NetFront NX'),
    UserAgentMatcher('(Nintendo 3DS)', 'NetFront NX'),
    UserAgentMatcher('(Silk)/(\\d+)\\.(\\d+)(?:\\.([0-9\\-]+)|)', 'Amazon Silk'),
    UserAgentMatcher('(Puffin)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)'),
    UserAgentMatcher('Windows Phone .{0,200}(Edge)/(\\d+)\\.(\\d+)', 'Edge Mobile'),
    UserAgentMatcher('(EdgiOS|EdgA)/(\\d+)\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'Edge Mobile'),
    UserAgentMatcher('(OculusBrowser)/(\\d+)\\.(\\d+).0.0(?:\\.([0-9\\-]+)|)', 'Oculus Browser'),
    UserAgentMatcher('(SamsungBrowser)/(\\d+)\\.(\\d+)', 'Samsung Internet'),
    UserAgentMatcher('(SznProhlizec)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'Seznam prohlížeč'),
    UserAgentMatcher('(coc_coc_browser)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'Coc Coc'),
    UserAgentMatcher('(baidubrowser)[/\\s](\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)', 'Baidu Browser'),
    UserAgentMatcher('(FlyFlow)/(\\d+)\\.(\\d+)', 'Baidu Explorer'),
    UserAgentMatcher('(MxBrowser)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'Maxthon'),
    UserAgentMatcher('(Crosswalk)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Line)/(\\d+)\\.(\\d+)\\.(\\d+)', 'LINE'),
    UserAgentMatcher('(MiuiBrowser)/(\\d+)\\.(\\d+)\\.(\\d+)', 'MiuiBrowser'),
    UserAgentMatcher('(Mint Browser)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Mint Browser'),
    UserAgentMatcher('(TopBuzz)/(\\d+).(\\d+).(\\d+)', 'TopBuzz'),
    UserAgentMatcher('Mozilla.{1,200}Android.{1,200}(GSA)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Google'),
    UserAgentMatcher('(MQQBrowser/Mini)(?:(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)', 'QQ Browser Mini'),
    UserAgentMatcher('(MQQBrowser)(?:/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)', 'QQ Browser Mobile'),
    UserAgentMatcher('(QQBrowser)(?:/(\\d+)(?:\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)|)', 'QQ Browser'),
    UserAgentMatcher('Mobile.{0,200}(DuckDuckGo)/(\\d+)', 'DuckDuckGo Mobile'),
    UserAgentMatcher('(Tenta/)(\\d+)\\.(\\d+)\\.(\\d+)', 'Tenta Browser'),
    UserAgentMatcher('Version/.{1,300}(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)', 'Chrome Mobile WebView'),
    UserAgentMatcher('; wv\\).{1,300}(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)', 'Chrome Mobile WebView'),
    UserAgentMatcher('(CrMo)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)', 'Chrome Mobile'),
    UserAgentMatcher('(CriOS)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)', 'Chrome Mobile iOS'),
    UserAgentMatcher('(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+) Mobile(?:[ /]|$)', 'Chrome Mobile'),
    UserAgentMatcher(' Mobile .{1,300}(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)', 'Chrome Mobile'),
    UserAgentMatcher('(chromeframe)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Chrome Frame'),
    UserAgentMatcher('(SLP Browser)/(\\d+)\\.(\\d+)', 'Tizen Browser'),
    UserAgentMatcher('(SE 2\\.X) MetaSr (\\d+)\\.(\\d+)', 'Sogou Explorer'),
    UserAgentMatcher('(Rackspace Monitoring)/(\\d+)\\.(\\d+)', 'RackspaceBot'),
    UserAgentMatcher('(PRTG Network Monitor)'),
    UserAgentMatcher('(PyAMF)/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(YaBrowser)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Yandex Browser'),
    UserAgentMatcher('(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+).{0,100} MRCHROME', 'Mail.ru Chromium Browser'),
    UserAgentMatcher('(AOL) (\\d+)\\.(\\d+); AOLBuild (\\d+)'),
    UserAgentMatcher('(PodCruncher|Downcast)[ /]?(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)(?:\\.(\\d+)|)'),
    UserAgentMatcher(' (BoxNotes)/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Whale)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+) Mobile(?:[ /]|$)', 'Whale'),
    UserAgentMatcher('(Whale)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Whale'),
    UserAgentMatcher('(1Password)/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Ghost)/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('PAN (GlobalProtect)/(\\d+)\\.(\\d+)\\.(\\d+) .{1,100} \\(X11; Linux x86_64\\)'),
    UserAgentMatcher('^(surveyon)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Surveyon'),
    UserAgentMatcher('(Slack_SSB)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Slack Desktop Client'),
    UserAgentMatcher('(HipChat)/?(\\d+|)', 'HipChat Desktop Client'),
    UserAgentMatcher('\\b(MobileIron|FireWeb|Jasmine|ANTGalio|Midori|Fresco|Lobo|PaleMoon|Maxthon|Lynx|OmniWeb|Dillo|Camino|Demeter|Fluid|Fennec|Epiphany|Shiira|Sunrise|Spotify|Flock|Netscape|Lunascape|WebPilot|NetFront|Netfront|Konqueror|SeaMonkey|Kazehakase|Vienna|Iceape|Iceweasel|IceWeasel|Iron|K-Meleon|Sleipnir|Galeon|GranParadiso|Opera Mini|iCab|NetNewsWire|ThunderBrowse|Iris|UP\\.Browser|Bunjalloo|Google Earth|Raven for Mac|Openwave|MacOutlook|Electron|OktaMobile)/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('Microsoft Office Outlook 12\\.\\d+\\.\\d+|MSOffice 12', 'Outlook', '2007'),
    UserAgentMatcher('Microsoft Outlook 14\\.\\d+\\.\\d+|MSOffice 14', 'Outlook', '2010'),
    UserAgentMatcher('Microsoft Outlook 15\\.\\d+\\.\\d+', 'Outlook', '2013'),
    UserAgentMatcher('Microsoft Outlook (?:Mail )?16\\.\\d+\\.\\d+|MSOffice 16', 'Outlook', '2016'),
    UserAgentMatcher('Microsoft Office (Word) 2014'),
    UserAgentMatcher('Outlook-Express\\/7\\.0', 'Windows Live Mail'),
    UserAgentMatcher('(Airmail) (\\d+)\\.(\\d+)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(Thunderbird)/(\\d+)\\.(\\d+)(?:\\.(\\d+(?:pre|))|)', 'Thunderbird'),
    UserAgentMatcher('(Postbox)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Postbox'),
    UserAgentMatcher('(Barca(?:Pro)?)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'Barca'),
    UserAgentMatcher('(Lotus-Notes)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'Lotus Notes'),
    UserAgentMatcher('Superhuman', 'Superhuman'),
    UserAgentMatcher('(Vivaldi)/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Edge?)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)(?:\\.(\\d+)|)', 'Edge'),
    UserAgentMatcher('(brave)/(\\d+)\\.(\\d+)\\.(\\d+) Chrome', 'Brave'),
    UserAgentMatcher('(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)[\\d.]{0,100} Iron[^/]', 'Iron'),
    UserAgentMatcher('\\b(Dolphin)(?: |HDCN/|/INT\\-)(\\d+)\\.(\\d+)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(HeadlessChrome)(?:/(\\d+)\\.(\\d+)\\.(\\d+)|)'),
    UserAgentMatcher('(Evolution)/(\\d+)\\.(\\d+)\\.(\\d+\\.\\d+)'),
    UserAgentMatcher('(RCM CardDAV plugin)/(\\d+)\\.(\\d+)\\.(\\d+(?:-dev|))'),
    UserAgentMatcher('(bingbot|Bolt|AdobeAIR|Jasmine|IceCat|Skyfire|Midori|Maxthon|Lynx|Arora|IBrowse|Dillo|Camino|Shiira|Fennec|Phoenix|Flock|Netscape|Lunascape|Epiphany|WebPilot|Opera Mini|Opera|NetFront|Netfront|Konqueror|Googlebot|SeaMonkey|Kazehakase|Vienna|Iceape|Iceweasel|IceWeasel|Iron|K-Meleon|Sleipnir|Galeon|GranParadiso|iCab|iTunes|MacAppStore|NetNewsWire|Space Bison|Stainless|Orca|Dolfin|BOLT|Minimo|Tizen Browser|Polaris|Abrowser|Planetweb|ICE Browser|mDolphin|qutebrowser|Otter|QupZilla|MailBar|kmail2|YahooMobileMail|ExchangeWebServices|ExchangeServicesClient|Dragon|Outlook-iOS-Android)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(Chromium|Chrome)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(IEMobile)[ /](\\d+)\\.(\\d+)', 'IE Mobile'),
    UserAgentMatcher('(BacaBerita App)\\/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('^(bPod|Pocket Casts|Player FM)$'),
    UserAgentMatcher('^(AlexaMediaPlayer|VLC)/(\\d+)\\.(\\d+)\\.([^.\\s]+)'),
    UserAgentMatcher('^(AntennaPod|WMPlayer|Zune|Podkicker|Radio|ExoPlayerDemo|Overcast|PocketTunes|NSPlayer|okhttp|DoggCatcher|QuickNews|QuickTime|Peapod|Podcasts|GoldenPod|VLC|Spotify|Miro|MediaGo|Juice|iPodder|gPodder|Banshee)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)'),
    UserAgentMatcher('^(Peapod|Liferea)/([^.\\s]+)\\.([^.\\s]+|)\\.?([^.\\s]+|)'),
    UserAgentMatcher('^(bPod|Player FM) BMID/(\\S+)'),
    UserAgentMatcher('^(Podcast ?Addict)/v(\\d+) '),
    UserAgentMatcher('^(Podcast ?Addict) ', 'PodcastAddict'),
    UserAgentMatcher('(Replay) AV'),
    UserAgentMatcher('(VOX) Music Player'),
    UserAgentMatcher('(CITA) RSS Aggregator/(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Pocket Casts)$'),
    UserAgentMatcher('(Player FM)$'),
    UserAgentMatcher('(LG Player|Doppler|FancyMusic|MediaMonkey|Clementine) (\\d+)\\.(\\d+)\\.?([^.\\s]+|)\\.?([^.\\s]+|)'),
    UserAgentMatcher('(philpodder)/(\\d+)\\.(\\d+)\\.?([^.\\s]+|)\\.?([^.\\s]+|)'),
    UserAgentMatcher('(Player FM|Pocket Casts|DoggCatcher|Spotify|MediaMonkey|MediaGo|BashPodder)'),
    UserAgentMatcher('(QuickTime)\\.(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Kinoma)(\\d+)'),
    UserAgentMatcher('(Fancy) Cloud Music (\\d+)\\.(\\d+)', 'FancyMusic'),
    UserAgentMatcher('EspnDownloadManager', 'ESPN'),
    UserAgentMatcher('(ESPN) Radio (\\d+)\\.(\\d+)(?:\\.(\\d+)|) ?(?:rv:(\\d+)|) '),
    UserAgentMatcher('(podracer|jPodder) v ?(\\d+)\\.(\\d+)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(ZDM)/(\\d+)\\.(\\d+)[; ]?'),
    UserAgentMatcher('(Zune|BeyondPod) (\\d+)(?:\\.(\\d+)|)[\\);]'),
    UserAgentMatcher('(WMPlayer)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('^(Lavf)', 'WMPlayer'),
    UserAgentMatcher('^(RSSRadio)[ /]?(\\d+|)'),
    UserAgentMatcher('(RSS_Radio) (\\d+)\\.(\\d+)', 'RSSRadio'),
    UserAgentMatcher('(Podkicker) \\S+/(\\d+)\\.(\\d+)\\.(\\d+)', 'Podkicker'),
    UserAgentMatcher('^(HTC) Streaming Player \\S+ / \\S+ / \\S+ / (\\d+)\\.(\\d+)(?:\\.(\\d+)|)'),
    UserAgentMatcher('^(Stitcher)/iOS'),
    UserAgentMatcher('^(Stitcher)/Android'),
    UserAgentMatcher('^(VLC) .{0,200}version (\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher(' (VLC) for'),
    UserAgentMatcher('(vlc)/(\\d+)\\.(\\d+)\\.(\\d+)', 'VLC'),
    UserAgentMatcher('^(foobar)\\S{1,10}/(\\d+)\\.(\\d+|)\\.?([\\da-z]+|)'),
    UserAgentMatcher('^(Clementine)\\S{1,10} (\\d+)\\.(\\d+|)\\.?(\\d+|)'),
    UserAgentMatcher('(amarok)/(\\d+)\\.(\\d+|)\\.?(\\d+|)', 'Amarok'),
    UserAgentMatcher('(Custom)-Feed Reader'),
    UserAgentMatcher('(iRider|Crazy Browser|SkipStone|iCab|Lunascape|Sleipnir|Maemo Browser) (\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(iCab|Lunascape|Opera|Android|Jasmine|Polaris|Microsoft SkyDriveSync|The Bat!) (\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(Kindle)/(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Android) Donut', None, '1', '2'),
    UserAgentMatcher('(Android) Eclair', None, '2', '1'),
    UserAgentMatcher('(Android) Froyo', None, '2', '2'),
    UserAgentMatcher('(Android) Gingerbread', None, '2', '3'),
    UserAgentMatcher('(Android) Honeycomb', None, '3'),
    UserAgentMatcher('(MSIE) (\\d+)\\.(\\d+).{0,100}XBLWP7', 'IE Large Screen'),
    UserAgentMatcher('(Nextcloud)'),
    UserAgentMatcher('(mirall)/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(ownCloud-android)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Owncloud'),
    UserAgentMatcher('(OC)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+) \\(Skype for Business\\)', 'Skype'),
    UserAgentMatcher('(OpenVAS)(?:-VT)?(?:[ \\/](\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)', 'OpenVAS Scanner'),
    UserAgentMatcher('(AnyConnect)\\/(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)'),
    UserAgentMatcher('compatible; monitis', 'Monitis'),
    UserAgentMatcher('(Obigo)InternetBrowser'),
    UserAgentMatcher('(Obigo)\\-Browser'),
    UserAgentMatcher('(Obigo|OBIGO)[^\\d]*(\\d+)(?:.(\\d+)|)', 'Obigo'),
    UserAgentMatcher('(MAXTHON|Maxthon) (\\d+)\\.(\\d+)', 'Maxthon'),
    UserAgentMatcher('(Maxthon|MyIE2|Uzbl|Shiira)', None, '0'),
    UserAgentMatcher('(BrowseX) \\((\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(NCSA_Mosaic)/(\\d+)\\.(\\d+)', 'NCSA Mosaic'),
    UserAgentMatcher('(POLARIS)/(\\d+)\\.(\\d+)', 'Polaris'),
    UserAgentMatcher('(Embider)/(\\d+)\\.(\\d+)', 'Polaris'),
    UserAgentMatcher('(BonEcho)/(\\d+)\\.(\\d+)\\.?([ab]?\\d+|)', 'Bon Echo'),
    UserAgentMatcher('(TopBuzz) com.alex.NewsMaster/(\\d+).(\\d+).(\\d+)', 'TopBuzz'),
    UserAgentMatcher('(TopBuzz) com.mobilesrepublic.newsrepublic/(\\d+).(\\d+).(\\d+)', 'TopBuzz'),
    UserAgentMatcher('(TopBuzz) com.topbuzz.videoen/(\\d+).(\\d+).(\\d+)', 'TopBuzz'),
    UserAgentMatcher('(iPod|iPhone|iPad).{1,200}GSA/(\\d+)\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|) Mobile', 'Google'),
    UserAgentMatcher('(iPod|iPhone|iPad).{1,200}Version/(\\d+)\\.(\\d+)(?:\\.(\\d+)|).{1,200}[ +]Safari', 'Mobile Safari'),
    UserAgentMatcher('(iPod|iPod touch|iPhone|iPad);.{0,30}CPU.{0,30}OS[ +](\\d+)_(\\d+)(?:_(\\d+)|).{0,30} AppleNews\\/\\d+\\.\\d+(?:\\.\\d+|)', 'Mobile Safari UI/WKWebView'),
    UserAgentMatcher('(iPod|iPhone|iPad).{1,200}Version/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'Mobile Safari UI/WKWebView'),
    UserAgentMatcher('(iPod|iPod touch|iPhone|iPad).{0,200} Safari', 'Mobile Safari'),
    UserAgentMatcher('(iPod|iPod touch|iPhone|iPad)', 'Mobile Safari UI/WKWebView'),
    UserAgentMatcher('(Watch)(\\d+),(\\d+)', 'Apple $1 App'),
    UserAgentMatcher('(Outlook-iOS)/\\d+\\.\\d+\\.prod\\.iphone \\((\\d+)\\.(\\d+)\\.(\\d+)\\)'),
    UserAgentMatcher('(AvantGo) (\\d+).(\\d+)'),
    UserAgentMatcher('(OneBrowser)/(\\d+).(\\d+)', 'ONE Browser'),
    UserAgentMatcher('(Avant)', None, '1'),
    UserAgentMatcher('(QtCarBrowser)', None, '1'),
    UserAgentMatcher('^(iBrowser/Mini)(\\d+).(\\d+)', 'iBrowser Mini'),
    UserAgentMatcher('^(iBrowser|iRAPP)/(\\d+).(\\d+)'),
    UserAgentMatcher('^(Nokia)', 'Nokia Services (WAP) Browser'),
    UserAgentMatcher('(NokiaBrowser)/(\\d+)\\.(\\d+).(\\d+)\\.(\\d+)', 'Nokia Browser'),
    UserAgentMatcher('(NokiaBrowser)/(\\d+)\\.(\\d+).(\\d+)', 'Nokia Browser'),
    UserAgentMatcher('(NokiaBrowser)/(\\d+)\\.(\\d+)', 'Nokia Browser'),
    UserAgentMatcher('(BrowserNG)/(\\d+)\\.(\\d+).(\\d+)', 'Nokia Browser'),
    UserAgentMatcher('(Series60)/5\\.0', 'Nokia Browser', '7', '0'),
    UserAgentMatcher('(Series60)/(\\d+)\\.(\\d+)', 'Nokia OSS Browser'),
    UserAgentMatcher('(S40OviBrowser)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)', 'Ovi Browser'),
    UserAgentMatcher('(Nokia)[EN]?(\\d+)'),
    UserAgentMatcher('(PlayBook).{1,200}RIM Tablet OS (\\d+)\\.(\\d+)\\.(\\d+)', 'BlackBerry WebKit'),
    UserAgentMatcher('(Black[bB]erry|BB10).{1,200}Version/(\\d+)\\.(\\d+)\\.(\\d+)', 'BlackBerry WebKit'),
    UserAgentMatcher('(Black[bB]erry)\\s?(\\d+)', 'BlackBerry'),
    UserAgentMatcher('(OmniWeb)/v(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Blazer)/(\\d+)\\.(\\d+)', 'Palm Blazer'),
    UserAgentMatcher('(Pre)/(\\d+)\\.(\\d+)', 'Palm Pre'),
    UserAgentMatcher('(ELinks)/(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(ELinks) \\((\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Links) \\((\\d+)\\.(\\d+)'),
    UserAgentMatcher('(QtWeb) Internet Browser/(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(PhantomJS)/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(AppleWebKit)/(\\d+)(?:\\.(\\d+)|)\\+ .{0,200} Safari', 'WebKit Nightly'),
    UserAgentMatcher('(Version)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|).{0,100}Safari/', 'Safari'),
    UserAgentMatcher('(Safari)/\\d+'),
    UserAgentMatcher('(OLPC)/Update(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(OLPC)/Update()\\.(\\d+)', None, '0'),
    UserAgentMatcher('(SEMC\\-Browser)/(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Teleca)', 'Teleca Browser'),
    UserAgentMatcher('(Phantom)/V(\\d+)\\.(\\d+)', 'Phantom Browser'),
    UserAgentMatcher('(Trident)/(7|8)\\.(0)', 'IE', '11'),
    UserAgentMatcher('(Trident)/(6)\\.(0)', 'IE', '10'),
    UserAgentMatcher('(Trident)/(5)\\.(0)', 'IE', '9'),
    UserAgentMatcher('(Trident)/(4)\\.(0)', 'IE', '8'),
    UserAgentMatcher('(Espial)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)'),
    UserAgentMatcher('(AppleWebKit)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Apple Mail'),
    UserAgentMatcher('(Firefox)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|$)'),
    UserAgentMatcher('(Firefox)/(\\d+)\\.(\\d+)(pre|[ab]\\d+[a-z]*|)'),
    UserAgentMatcher('([MS]?IE) (\\d+)\\.(\\d+)', 'IE'),
    UserAgentMatcher('(python-requests)/(\\d+)\\.(\\d+)', 'Python Requests'),
    UserAgentMatcher('\\b(Windows-Update-Agent|WindowsPowerShell|Microsoft-CryptoAPI|SophosUpdateManager|SophosAgent|Debian APT-HTTP|Ubuntu APT-HTTP|libcurl-agent|libwww-perl|urlgrabber|curl|PycURL|Wget|wget2|aria2|Axel|OpenBSD ftp|lftp|jupdate|insomnia|fetch libfetch|akka-http|got|CloudCockpitBackend|ReactorNetty|axios|Jersey|Vert.x-WebClient|Apache-CXF|Go-CF-client|go-resty|AHC|HTTPie)(?:[ /](\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)'),
    UserAgentMatcher('^(cf)\\/(\\d+)\\.(\\d+)\\.(\\S+)', 'CloudFoundry'),
    UserAgentMatcher('^(sap-leonardo-iot-sdk-nodejs) \\/ (\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('^(SAP NetWeaver Application Server) \\(1.0;(\\d{1})(\\d{2})\\)'),
    UserAgentMatcher('^(\\w+-HTTPClient)\\/(\\d+)\\.(\\d+)-(\\S+)', 'HTTPClient'),
    UserAgentMatcher('^(go-cli)\\s(\\d+)\\.(\\d+).(\\S+)'),
    UserAgentMatcher('^(Java-EurekaClient|Java-EurekaClient-Replication|HTTPClient|lua-resty-http)\\/v?(\\d+)\\.(\\d+)\\.?(\\d*)'),
    UserAgentMatcher('^(ping-service|sap xsuaa|Node-oauth|Site24x7|SAP CPI|JAEGER_SECURITY)'),
    UserAgentMatcher('(Python/3\\.\\d{1,3} aiohttp)/(\\d+)\\.(\\d+)\\.(\\d+)', 'Python aiohttp'),
    UserAgentMatcher('(Java)[/ ]?\\d{1}\\.(\\d+)\\.(\\d+)[_-]*([a-zA-Z0-9]+|)'),
    UserAgentMatcher('(Java)[/ ]?(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(minio-go)/v(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('^(ureq)[/ ](\\d+)\\.(\\d+).(\\d+)'),
    UserAgentMatcher('^(http\\.rb)/(\\d+)\\.(\\d+).(\\d+)'),
    UserAgentMatcher('^(GuzzleHttp)/(\\d+)\\.(\\d+).(\\d+)'),
    UserAgentMatcher('^(grab)\\b'),
    UserAgentMatcher('^(Cyberduck)/(\\d+)\\.(\\d+)\\.(\\d+)(?:\\.\\d+|)'),
    UserAgentMatcher('^(S3 Browser) (\\d+)[.-](\\d+)[.-](\\d+)(?:\\s*https?://s3browser\\.com|)'),
    UserAgentMatcher('(S3Gof3r)'),
    UserAgentMatcher('\\b(ibm-cos-sdk-(?:core|java|js|python))/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)'),
    UserAgentMatcher('^(rusoto)/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('^(rclone)/v(\\d+)\\.(\\d+)'),
    UserAgentMatcher('^(Roku)/DVP-(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Kurio)\\/(\\d+)\\.(\\d+)\\.(\\d+)', 'Kurio App'),
    UserAgentMatcher('^(Box(?: Sync)?)/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('^(ViaFree|Viafree)-(?:tvOS-)?[A-Z]{2}/(\\d+)\\.(\\d+)\\.(\\d+)', 'ViaFree'),
    UserAgentMatcher('(Transmit)/(\\d+)\\.(\\d+)\\.(\\d+)'),
    UserAgentMatcher('(Download Master)'),
    UserAgentMatcher('\\b(HTTrack) (\\d+)\\.(\\d+)(?:[\\.\\-](\\d+)|)'),
    UserAgentMatcher('SerenityOS', 'SerenityOS Browser'),
], [
    OSMatcher('HbbTV/\\d+\\.\\d+\\.\\d+ \\( ;(LG)E ;NetCast 4.0', None, '2013'),
    OSMatcher('HbbTV/\\d+\\.\\d+\\.\\d+ \\( ;(LG)E ;NetCast 3.0', None, '2012'),
    OSMatcher('HbbTV/1.1.1 \\(;;;;;\\) Maple_2011', 'Samsung', '2011'),
    OSMatcher('HbbTV/\\d+\\.\\d+\\.\\d+ \\(;(Samsung);SmartTV([0-9]{4});.{0,200}FXPDEUC', None, None, 'UE40F7000'),
    OSMatcher('HbbTV/\\d+\\.\\d+\\.\\d+ \\(;(Samsung);SmartTV([0-9]{4});.{0,200}MST12DEUC', None, None, 'UE32F4500'),
    OSMatcher('HbbTV/1\\.1\\.1 \\(; (Philips);.{0,200}NETTV/4', None, '2013'),
    OSMatcher('HbbTV/1\\.1\\.1 \\(; (Philips);.{0,200}NETTV/3', None, '2012'),
    OSMatcher('HbbTV/1\\.1\\.1 \\(; (Philips);.{0,200}NETTV/2', None, '2011'),
    OSMatcher('HbbTV/\\d+\\.\\d+\\.\\d+.{0,100}(firetv)-firefox-plugin (\\d+).(\\d+).(\\d+)', 'FireHbbTV'),
    OSMatcher('HbbTV/\\d+\\.\\d+\\.\\d+ \\(.{0,30}; ?([a-zA-Z]+) ?;.{0,30}(201[1-9]).{0,30}\\)'),
    OSMatcher('AspiegelBot|PetalBot', 'Other'),
    OSMatcher('(Windows Phone) (?:OS[ /])?(\\d+)\\.(\\d+)'),
    OSMatcher('(CPU[ +]OS|iPhone[ +]OS|CPU[ +]iPhone)[ +]+(\\d+)[_\\.](\\d+)(?:[_\\.](\\d+)|).{0,100}Outlook-iOS-Android', 'iOS'),
    OSMatcher('ArcGIS\\.?(iOS|Android)-\\d+\\.\\d+(?:\\.\\d+|)(?:[^\\/]{1,50}|)\\/(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)'),
    OSMatcher('ArcGISRuntime-(?:Android|iOS)\\/\\d+\\.\\d+(?:\\.\\d+|) \\((Android|iOS) (\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|);'),
    OSMatcher('(Android)[ \\-/](\\d+)(?:\\.(\\d+)|)(?:[.\\-]([a-z0-9]+)|)'),
    OSMatcher('(Android) Donut', None, '1', '2'),
    OSMatcher('(Android) Eclair', None, '2', '1'),
    OSMatcher('(Android) Froyo', None, '2', '2'),
    OSMatcher('(Android) Gingerbread', None, '2', '3'),
    OSMatcher('(Android) Honeycomb', None, '3'),
    OSMatcher('(Android) (\\d+);'),
    OSMatcher('(Android): (\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|);'),
    OSMatcher('^UCWEB.{0,200}; (Adr) (\\d+)\\.(\\d+)(?:[.\\-]([a-z0-9]{1,100})|);', 'Android'),
    OSMatcher('^UCWEB.{0,200}; (iPad|iPh|iPd) OS (\\d+)_(\\d+)(?:_(\\d+)|);', 'iOS'),
    OSMatcher('^UCWEB.{0,200}; (wds) (\\d+)\\.(\\d+)(?:\\.(\\d+)|);', 'Windows Phone'),
    OSMatcher('^(JUC).{0,200}; ?U; ?(?:Android|)(\\d+)\\.(\\d+)(?:[\\.\\-]([a-z0-9]{1,100})|)', 'Android'),
    OSMatcher('(android)\\s(?:mobile\\/)(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)', 'Android'),
    OSMatcher('Quest', 'Android'),
    OSMatcher('(Silk-Accelerated=[a-z]{4,5})', 'Android'),
    OSMatcher('(x86_64|aarch64)\\ (\\d+)\\.(\\d+)\\.(\\d+).{0,100}Chrome.{0,100}(?:CitrixChromeApp)$', 'Chrome OS'),
    OSMatcher('(XBLWP7)', 'Windows Phone'),
    OSMatcher('(Windows ?Mobile)', 'Windows Mobile'),
    OSMatcher('(Windows 10)', 'Windows', '10'),
    OSMatcher('(Windows (?:NT 5\\.2|NT 5\\.1))', 'Windows', 'XP'),
    OSMatcher('(Win(?:dows NT |32NT\\/)6\\.1)', 'Windows', '7'),
    OSMatcher('(Win(?:dows NT |32NT\\/)6\\.0)', 'Windows', 'Vista'),
    OSMatcher('(Win 9x 4\\.90)', 'Windows', 'ME'),
    OSMatcher('(Windows NT 6\\.2; ARM;)', 'Windows', 'RT'),
    OSMatcher('(Win(?:dows NT |32NT\\/)6\\.2)', 'Windows', '8'),
    OSMatcher('(Windows NT 6\\.3; ARM;)', 'Windows', 'RT 8', '1'),
    OSMatcher('(Win(?:dows NT |32NT\\/)6\\.3)', 'Windows', '8', '1'),
    OSMatcher('(Win(?:dows NT |32NT\\/)6\\.4)', 'Windows', '10'),
    OSMatcher('(Windows NT 10\\.0)', 'Windows', '10'),
    OSMatcher('(Windows NT 5\\.0)', 'Windows', '2000'),
    OSMatcher('(WinNT4.0)', 'Windows', 'NT 4.0'),
    OSMatcher('(Windows ?CE)', 'Windows', 'CE'),
    OSMatcher('Win(?:dows)? ?(95|98|3.1|NT|ME|2000|XP|Vista|7|CE)', 'Windows', '$1'),
    OSMatcher('Win16', 'Windows', '3.1'),
    OSMatcher('Win32', 'Windows', '95'),
    OSMatcher('^Box.{0,200}Windows/([\\d.]+);', 'Windows', '$1'),
    OSMatcher('(Tizen)[/ ](\\d+)\\.(\\d+)'),
    OSMatcher('((?:Mac[ +]?|; )OS[ +]X)[\\s+/](?:(\\d+)[_.](\\d+)(?:[_.](\\d+)|)|Mach-O)', 'Mac OS X'),
    OSMatcher('Mac OS X\\s.{1,50}\\s(\\d+).(\\d+).(\\d+)', 'Mac OS X', '$1', '$2', '$3'),
    OSMatcher(' (Dar)(win)/(9).(\\d+).{0,100}\\((?:i386|x86_64|Power Macintosh)\\)', 'Mac OS X', '10', '5'),
    OSMatcher(' (Dar)(win)/(10).(\\d+).{0,100}\\((?:i386|x86_64)\\)', 'Mac OS X', '10', '6'),
    OSMatcher(' (Dar)(win)/(11).(\\d+).{0,100}\\((?:i386|x86_64)\\)', 'Mac OS X', '10', '7'),
    OSMatcher(' (Dar)(win)/(12).(\\d+).{0,100}\\((?:i386|x86_64)\\)', 'Mac OS X', '10', '8'),
    OSMatcher(' (Dar)(win)/(13).(\\d+).{0,100}\\((?:i386|x86_64)\\)', 'Mac OS X', '10', '9'),
    OSMatcher('Mac_PowerPC', 'Mac OS'),
    OSMatcher('(?:PPC|Intel) (Mac OS X)'),
    OSMatcher('^Box.{0,200};(Darwin)/(10)\\.(1\\d)(?:\\.(\\d+)|)', 'Mac OS X'),
    OSMatcher('(Apple\\s?TV)(?:/(\\d+)\\.(\\d+)|)', 'ATV OS X'),
    OSMatcher('(CPU[ +]OS|iPhone[ +]OS|CPU[ +]iPhone|CPU IPhone OS|CPU iPad OS)[ +]+(\\d+)[_\\.](\\d+)(?:[_\\.](\\d+)|)', 'iOS'),
    OSMatcher('(iPhone|iPad|iPod); Opera', 'iOS'),
    OSMatcher('(iPhone|iPad|iPod).{0,100}Mac OS X.{0,100}Version/(\\d+)\\.(\\d+)', 'iOS'),
    OSMatcher('(CFNetwork)/(5)48\\.0\\.3.{0,100} Darwin/11\\.0\\.0', 'iOS'),
    OSMatcher('(CFNetwork)/(5)48\\.(0)\\.4.{0,100} Darwin/(1)1\\.0\\.0', 'iOS'),
    OSMatcher('(CFNetwork)/(5)48\\.(1)\\.4', 'iOS'),
    OSMatcher('(CFNetwork)/(4)85\\.1(3)\\.9', 'iOS'),
    OSMatcher('(CFNetwork)/(6)09\\.(1)\\.4', 'iOS'),
    OSMatcher('(CFNetwork)/(6)(0)9', 'iOS'),
    OSMatcher('(CFNetwork)/6(7)2\\.(1)\\.13', 'iOS'),
    OSMatcher('(CFNetwork)/6(7)2\\.(1)\\.(1)4', 'iOS'),
    OSMatcher('(CF)(Network)/6(7)(2)\\.1\\.15', 'iOS', '7', '1'),
    OSMatcher('(CFNetwork)/6(7)2\\.(0)\\.(?:2|8)', 'iOS'),
    OSMatcher('(CFNetwork)/709\\.1', 'iOS', '8', '0.b5'),
    OSMatcher('(CF)(Network)/711\\.(\\d)', 'iOS', '8'),
    OSMatcher('(CF)(Network)/(720)\\.(\\d)', 'Mac OS X', '10', '10'),
    OSMatcher('(CF)(Network)/(760)\\.(\\d)', 'Mac OS X', '10', '11'),
    OSMatcher('CFNetwork/7.{0,100} Darwin/15\\.4\\.\\d+', 'iOS', '9', '3', '1'),
    OSMatcher('CFNetwork/7.{0,100} Darwin/15\\.5\\.\\d+', 'iOS', '9', '3', '2'),
    OSMatcher('CFNetwork/7.{0,100} Darwin/15\\.6\\.\\d+', 'iOS', '9', '3', '5'),
    OSMatcher('(CF)(Network)/758\\.(\\d)', 'iOS', '9'),
    OSMatcher('CFNetwork/808\\.3 Darwin/16\\.3\\.\\d+', 'iOS', '10', '2', '1'),
    OSMatcher('(CF)(Network)/808\\.(\\d)', 'iOS', '10'),
    OSMatcher('CFNetwork/.{0,100} Darwin/17\\.\\d+.{0,100}\\(x86_64\\)', 'Mac OS X', '10', '13'),
    OSMatcher('CFNetwork/.{0,100} Darwin/16\\.\\d+.{0,100}\\(x86_64\\)', 'Mac OS X', '10', '12'),
    OSMatcher('CFNetwork/8.{0,100} Darwin/15\\.\\d+.{0,100}\\(x86_64\\)', 'Mac OS X', '10', '11'),
    OSMatcher('CFNetwork/.{0,100} Darwin/(9)\\.\\d+', 'iOS', '1'),
    OSMatcher('CFNetwork/.{0,100} Darwin/(10)\\.\\d+', 'iOS', '4'),
    OSMatcher('CFNetwork/.{0,100} Darwin/(11)\\.\\d+', 'iOS', '5'),
    OSMatcher('CFNetwork/.{0,100} Darwin/(13)\\.\\d+', 'iOS', '6'),
    OSMatcher('CFNetwork/6.{0,100} Darwin/(14)\\.\\d+', 'iOS', '7'),
    OSMatcher('CFNetwork/7.{0,100} Darwin/(14)\\.\\d+', 'iOS', '8', '0'),
    OSMatcher('CFNetwork/7.{0,100} Darwin/(15)\\.\\d+', 'iOS', '9', '0'),
    OSMatcher('CFNetwork/8.{0,100} Darwin/16\\.5\\.\\d+', 'iOS', '10', '3'),
    OSMatcher('CFNetwork/8.{0,100} Darwin/16\\.6\\.\\d+', 'iOS', '10', '3', '2'),
    OSMatcher('CFNetwork/8.{0,100} Darwin/16\\.7\\.\\d+', 'iOS', '10', '3', '3'),
    OSMatcher('CFNetwork/8.{0,100} Darwin/(16)\\.\\d+', 'iOS', '10'),
    OSMatcher('CFNetwork/8.{0,100} Darwin/17\\.0\\.\\d+', 'iOS', '11', '0'),
    OSMatcher('CFNetwork/8.{0,100} Darwin/17\\.2\\.\\d+', 'iOS', '11', '1'),
    OSMatcher('CFNetwork/8.{0,100} Darwin/17\\.3\\.\\d+', 'iOS', '11', '2'),
    OSMatcher('CFNetwork/8.{0,100} Darwin/17\\.4\\.\\d+', 'iOS', '11', '2', '6'),
    OSMatcher('CFNetwork/8.{0,100} Darwin/17\\.5\\.\\d+', 'iOS', '11', '3'),
    OSMatcher('CFNetwork/9.{0,100} Darwin/17\\.6\\.\\d+', 'iOS', '11', '4'),
    OSMatcher('CFNetwork/9.{0,100} Darwin/17\\.7\\.\\d+', 'iOS', '11', '4', '1'),
    OSMatcher('CFNetwork/8.{0,100} Darwin/(17)\\.\\d+', 'iOS', '11'),
    OSMatcher('CFNetwork/9.{0,100} Darwin/18\\.0\\.\\d+', 'iOS', '12', '0'),
    OSMatcher('CFNetwork/9.{0,100} Darwin/18\\.2\\.\\d+', 'iOS', '12', '1'),
    OSMatcher('CFNetwork/9.{0,100} Darwin/18\\.5\\.\\d+', 'iOS', '12', '2'),
    OSMatcher('CFNetwork/9.{0,100} Darwin/18\\.6\\.\\d+', 'iOS', '12', '3'),
    OSMatcher('CFNetwork/9.{0,100} Darwin/18\\.7\\.\\d+', 'iOS', '12', '4'),
    OSMatcher('CFNetwork/9.{0,100} Darwin/(18)\\.\\d+', 'iOS', '12'),
    OSMatcher('CFNetwork/11.{0,100} Darwin/19\\.2\\.\\d+', 'iOS', '13', '3'),
    OSMatcher('CFNetwork/11.{0,100} Darwin/19\\.3\\.\\d+', 'iOS', '13', '3', '1'),
    OSMatcher('CFNetwork/11.{0,100} Darwin/19\\.4\\.\\d+', 'iOS', '13', '4'),
    OSMatcher('CFNetwork/11.{0,100} Darwin/19\\.5\\.\\d+', 'iOS', '13', '5'),
    OSMatcher('CFNetwork/11.{0,100} Darwin/19\\.6\\.\\d+', 'iOS', '13', '6'),
    OSMatcher('CFNetwork/1[01].{0,100} Darwin/19\\.\\d+', 'iOS', '13'),
    OSMatcher('CFNetwork/12.{0,100} Darwin/20\\.1\\.\\d+', 'iOS', '14', '2'),
    OSMatcher('CFNetwork/12.{0,100} Darwin/20\\.2\\.\\d+', 'iOS', '14', '3'),
    OSMatcher('CFNetwork/12.{0,100} Darwin/20\\.3\\.\\d+', 'iOS', '14', '4'),
    OSMatcher('CFNetwork/12.{0,100} Darwin/20\\.4\\.\\d+', 'iOS', '14', '5'),
    OSMatcher('CFNetwork/12.{0,100} Darwin/20\\.5\\.\\d+', 'iOS', '14', '6'),
    OSMatcher('CFNetwork/12.{0,100} Darwin/20\\.6\\.\\d+', 'iOS', '14', '8'),
    OSMatcher('CFNetwork/.{0,100} Darwin/(20)\\.\\d+', 'iOS', '14'),
    OSMatcher('CFNetwork/13.{0,100} Darwin/21\\.0\\.\\d+', 'iOS', '15', '0'),
    OSMatcher('CFNetwork/13.{0,100} Darwin/21\\.1\\.\\d+', 'iOS', '15', '1'),
    OSMatcher('CFNetwork/13.{0,100} Darwin/21\\.2\\.\\d+', 'iOS', '15', '2'),
    OSMatcher('CFNetwork/13.{0,100} Darwin/21\\.3\\.\\d+', 'iOS', '15', '3'),
    OSMatcher('CFNetwork/13.{0,100} Darwin/21\\.4\\.\\d+', 'iOS', '15', '4'),
    OSMatcher('CFNetwork/13.{0,100} Darwin/21\\.5\\.\\d+', 'iOS', '15', '5'),
    OSMatcher('CFNetwork/13.{0,100} Darwin/21\\.6\\.\\d+', 'iOS', '15', '6'),
    OSMatcher('CFNetwork/.{0,100} Darwin/(21)\\.\\d+', 'iOS', '15'),
    OSMatcher('CFNetwork/.{0,100} Darwin/22\\.0\\.\\d+', 'iOS', '16', '0'),
    OSMatcher('CFNetwork/.{0,100} Darwin/22\\.1\\.\\d+', 'iOS', '16', '1'),
    OSMatcher('CFNetwork/.{0,100} Darwin/22\\.2\\.\\d+', 'iOS', '16', '2'),
    OSMatcher('CFNetwork/.{0,100} Darwin/22\\.3\\.\\d+', 'iOS', '16', '3'),
    OSMatcher('CFNetwork/.{0,100} Darwin/22\\.4\\.\\d+', 'iOS', '16', '4'),
    OSMatcher('CFNetwork/.{0,100} Darwin/(22)\\.\\d+', 'iOS', '16'),
    OSMatcher('CFNetwork/.{0,100} Darwin/', 'iOS'),
    OSMatcher('\\b(iOS[ /]|iOS; |iPhone(?:/| v|[ _]OS[/,]|; | OS : |\\d,\\d/|\\d,\\d; )|iPad/)(\\d{1,2})[_\\.](\\d{1,2})(?:[_\\.](\\d+)|)', 'iOS'),
    OSMatcher('\\((iOS);'),
    OSMatcher('(watchOS)[/ ](\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'WatchOS'),
    OSMatcher('Outlook-(iOS)/\\d+\\.\\d+\\.prod\\.iphone'),
    OSMatcher('(iPod|iPhone|iPad)', 'iOS'),
    OSMatcher('(tvOS)[/ ](\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'tvOS'),
    OSMatcher('(CrOS) [a-z0-9_]+ (\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'Chrome OS'),
    OSMatcher('([Dd]ebian)', 'Debian'),
    OSMatcher('(Linux Mint)(?:/(\\d+)|)'),
    OSMatcher('(Mandriva)(?: Linux|)/(?:[\\d.-]+m[a-z]{2}(\\d+).(\\d)|)'),
    OSMatcher('(Symbian[Oo][Ss])[/ ](\\d+)\\.(\\d+)', 'Symbian OS'),
    OSMatcher('(Symbian/3).{1,200}NokiaBrowser/7\\.3', 'Symbian^3 Anna'),
    OSMatcher('(Symbian/3).{1,200}NokiaBrowser/7\\.4', 'Symbian^3 Belle'),
    OSMatcher('(Symbian/3)', 'Symbian^3'),
    OSMatcher('\\b(Series 60|SymbOS|S60Version|S60V\\d|S60\\b)', 'Symbian OS'),
    OSMatcher('(MeeGo)'),
    OSMatcher('Symbian [Oo][Ss]', 'Symbian OS'),
    OSMatcher('Series40;', 'Nokia Series 40'),
    OSMatcher('Series30Plus;', 'Nokia Series 30 Plus'),
    OSMatcher('(BB10);.{1,200}Version/(\\d+)\\.(\\d+)\\.(\\d+)', 'BlackBerry OS'),
    OSMatcher('(Black[Bb]erry)[0-9a-z]+/(\\d+)\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'BlackBerry OS'),
    OSMatcher('(Black[Bb]erry).{1,200}Version/(\\d+)\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'BlackBerry OS'),
    OSMatcher('(RIM Tablet OS) (\\d+)\\.(\\d+)\\.(\\d+)', 'BlackBerry Tablet OS'),
    OSMatcher('(Play[Bb]ook)', 'BlackBerry Tablet OS'),
    OSMatcher('(Black[Bb]erry)', 'BlackBerry OS'),
    OSMatcher('(K[Aa][Ii]OS)\\/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'KaiOS'),
    OSMatcher('\\((?:Mobile|Tablet);.{1,200}Gecko/18.0 Firefox/\\d+\\.\\d+', 'Firefox OS', '1', '0', '1'),
    OSMatcher('\\((?:Mobile|Tablet);.{1,200}Gecko/18.1 Firefox/\\d+\\.\\d+', 'Firefox OS', '1', '1'),
    OSMatcher('\\((?:Mobile|Tablet);.{1,200}Gecko/26.0 Firefox/\\d+\\.\\d+', 'Firefox OS', '1', '2'),
    OSMatcher('\\((?:Mobile|Tablet);.{1,200}Gecko/28.0 Firefox/\\d+\\.\\d+', 'Firefox OS', '1', '3'),
    OSMatcher('\\((?:Mobile|Tablet);.{1,200}Gecko/30.0 Firefox/\\d+\\.\\d+', 'Firefox OS', '1', '4'),
    OSMatcher('\\((?:Mobile|Tablet);.{1,200}Gecko/32.0 Firefox/\\d+\\.\\d+', 'Firefox OS', '2', '0'),
    OSMatcher('\\((?:Mobile|Tablet);.{1,200}Gecko/34.0 Firefox/\\d+\\.\\d+', 'Firefox OS', '2', '1'),
    OSMatcher('\\((?:Mobile|Tablet);.{1,200}Firefox/\\d+\\.\\d+', 'Firefox OS'),
    OSMatcher('(BREW)[ /](\\d+)\\.(\\d+)\\.(\\d+)'),
    OSMatcher('(BREW);'),
    OSMatcher('(Brew MP|BMP)[ /](\\d+)\\.(\\d+)\\.(\\d+)', 'Brew MP'),
    OSMatcher('BMP;', 'Brew MP'),
    OSMatcher('(GoogleTV)(?: (\\d+)\\.(\\d+)(?:\\.(\\d+)|)|/[\\da-z]+)'),
    OSMatcher('(WebTV)/(\\d+).(\\d+)'),
    OSMatcher('(CrKey)(?:[/](\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)', 'Chromecast'),
    OSMatcher('(hpw|web)OS/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)', 'webOS'),
    OSMatcher('(VRE);'),
    OSMatcher('(Fedora|Red Hat|PCLinuxOS|Puppy|Ubuntu|Kindle|Bada|Sailfish|Lubuntu|BackTrack|Slackware|(?:Free|Open|Net|\\b)BSD)[/ ](\\d+)\\.(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)'),
    OSMatcher('(Linux)[ /](\\d+)\\.(\\d+)(?:\\.(\\d+)|).{0,100}gentoo', 'Gentoo'),
    OSMatcher('\\((Bada);'),
    OSMatcher('(Windows|Android|WeTab|Maemo|Web0S)'),
    OSMatcher('(Ubuntu|Kubuntu|Arch Linux|CentOS|Slackware|Gentoo|openSUSE|SUSE|Red Hat|Fedora|PCLinuxOS|Mageia|SerenityOS|(?:Free|Open|Net|\\b)BSD)'),
    OSMatcher('(Linux)(?:[ /](\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)'),
    OSMatcher('SunOS', 'Solaris'),
    OSMatcher('\\(linux-gnu\\)', 'Linux'),
    OSMatcher('\\(x86_64-redhat-linux-gnu\\)', 'Red Hat'),
    OSMatcher('\\((freebsd)(\\d+)\\.(\\d+)\\)', 'FreeBSD'),
    OSMatcher('linux', 'Linux'),
    OSMatcher('^(Roku)/DVP-(\\d+)\\.(\\d+)'),
], [
    DeviceMatcher('^.{0,100}?(?:(?:iPhone|Windows CE|Windows Phone|Android).{0,300}(?:(?:Bot|Yeti)-Mobile|YRSpider|BingPreview|bots?/\\d|(?:bot|spider)\\.html)|AdsBot-Google-Mobile.{0,200}iPhone)', 'i', 'Spider', 'Spider', 'Smartphone'),
    DeviceMatcher('^.{0,100}?(?:DoCoMo|\\bMOT\\b|\\bLG\\b|Nokia|Samsung|SonyEricsson).{0,200}(?:(?:Bot|Yeti)-Mobile|bots?/\\d|(?:bot|crawler)\\.html|(?:jump|google|Wukong)bot|ichiro/mobile|/spider|YahooSeeker)', 'i', 'Spider', 'Spider', 'Feature Phone'),
    DeviceMatcher(' PTST/\\d+(?:\\.\\d+|)$', None, 'Spider', 'Spider'),
    DeviceMatcher('X11; Datanyze; Linux', None, 'Spider', 'Spider'),
    DeviceMatcher('Mozilla.{1,100}Mobile.{1,100}(AspiegelBot|PetalBot)', None, 'Spider', 'Spider', 'Smartphone'),
    DeviceMatcher('Mozilla.{0,200}(AspiegelBot|PetalBot)', None, 'Spider', 'Spider', 'Desktop'),
    DeviceMatcher('\\bSmartWatch {0,2}\\( {0,2}([^;]{1,200}) {0,2}; {0,2}([^;]{1,200}) {0,2};', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('Android Application[^\\-]{1,300} - (Sony) ?(Ericsson|) (.{1,200}) \\w{1,20} - ', None, '$1 $2', '$1$2', '$3'),
    DeviceMatcher('Android Application[^\\-]{1,300} - (?:HTC|HUAWEI|LGE|LENOVO|MEDION|TCT) (HTC|HUAWEI|LG|LENOVO|MEDION|ALCATEL)[ _\\-](.{1,200}) \\w{1,20} - ', 'i', '$1 $2', '$1', '$2'),
    DeviceMatcher('Android Application[^\\-]{1,300} - ([^ ]+) (.{1,200}) \\w{1,20} - ', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('; {0,2}([BLRQ]C\\d{4}[A-Z]{1,100}?)(?: Build|\\) AppleWebKit)', None, '3Q $1', '3Q', '$1'),
    DeviceMatcher('; {0,2}(?:3Q_)([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '3Q $1', '3Q', '$1'),
    DeviceMatcher('Android [34].{0,200}; {0,2}(A100|A101|A110|A200|A210|A211|A500|A501|A510|A511|A700(?: Lite| 3G|)|A701|B1-A71|A1-\\d{3}|B1-\\d{3}|V360|V370|W500|W500P|W501|W501P|W510|W511|W700|Slider SL101|DA22[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Acer', '$1'),
    DeviceMatcher('; {0,2}Acer Iconia Tab ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Acer', '$1'),
    DeviceMatcher('; {0,2}(Z1[1235]0|E320[^/]{0,10}|S500|S510|Liquid[^;/]{0,30}|Iconia A\\d+)(?: Build|\\) AppleWebKit)', None, '$1', 'Acer', '$1'),
    DeviceMatcher('; {0,2}(Acer |ACER )([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Acer', '$2'),
    DeviceMatcher('; {0,2}(Advent |)(Vega(?:Bean|Comb|)).{0,200}?(?: Build|\\) AppleWebKit)', None, '$1$2', 'Advent', '$2'),
    DeviceMatcher('; {0,2}(Ainol |)((?:NOVO|[Nn]ovo)[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Ainol', '$2'),
    DeviceMatcher('; {0,2}AIRIS[ _\\-]?([^/;\\)]+) {0,2}(?:;|\\)|Build)', 'i', '$1', 'Airis', '$1'),
    DeviceMatcher('; {0,2}(OnePAD[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', '$1', 'Airis', '$1'),
    DeviceMatcher('; {0,2}Airpad[ \\-]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Airpad $1', 'Airpad', '$1'),
    DeviceMatcher('; {0,2}(one ?touch) (EVO7|T10|T20)(?: Build|\\) AppleWebKit)', None, 'Alcatel One Touch $2', 'Alcatel', 'One Touch $2'),
    DeviceMatcher('; {0,2}(?:alcatel[ _]|)(?:(?:one[ _]?touch[ _])|ot[ \\-])([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', 'Alcatel One Touch $1', 'Alcatel', 'One Touch $1'),
    DeviceMatcher('; {0,2}(TCL)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('; {0,2}(Vodafone Smart II|Optimus_Madrid)(?: Build|\\) AppleWebKit)', None, 'Alcatel $1', 'Alcatel', '$1'),
    DeviceMatcher('; {0,2}BASE_Lutea_3(?: Build|\\) AppleWebKit)', None, 'Alcatel One Touch 998', 'Alcatel', 'One Touch 998'),
    DeviceMatcher('; {0,2}BASE_Varia(?: Build|\\) AppleWebKit)', None, 'Alcatel One Touch 918D', 'Alcatel', 'One Touch 918D'),
    DeviceMatcher('; {0,2}((?:FINE|Fine)\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Allfine', '$1'),
    DeviceMatcher('; {0,2}(ALLVIEW[ _]?|Allview[ _]?)((?:Speed|SPEED).{0,200}?)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Allview', '$2'),
    DeviceMatcher('; {0,2}(ALLVIEW[ _]?|Allview[ _]?|)(AX1_Shine|AX2_Frenzy)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Allview', '$2'),
    DeviceMatcher('; {0,2}(ALLVIEW[ _]?|Allview[ _]?)([^;/]*?)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Allview', '$2'),
    DeviceMatcher('; {0,2}(A13-MID)(?: Build|\\) AppleWebKit)', None, '$1', 'Allwinner', '$1'),
    DeviceMatcher('; {0,2}(Allwinner)[ _\\-]?([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Allwinner', '$1'),
    DeviceMatcher('; {0,2}(A651|A701B?|A702|A703|A705|A706|A707|A711|A712|A713|A717|A722|A785|A801|A802|A803|A901|A902|A1002|A1003|A1006|A1007|A9701|A9703|Q710|Q80)(?: Build|\\) AppleWebKit)', None, '$1', 'Amaway', '$1'),
    DeviceMatcher('; {0,2}(?:AMOI|Amoi)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Amoi $1', 'Amoi', '$1'),
    DeviceMatcher('^(?:AMOI|Amoi)[ _]([^;/]{1,100}?) Linux', None, 'Amoi $1', 'Amoi', '$1'),
    DeviceMatcher('; {0,2}(MW(?:0[789]|10)[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Aoc', '$1'),
    DeviceMatcher('; {0,2}(G7|M1013|M1015G|M11[CG]?|M-?12[B]?|M15|M19[G]?|M30[ACQ]?|M31[GQ]|M32|M33[GQ]|M36|M37|M38|M701T|M710|M712B|M713|M715G|M716G|M71(?:G|GS|T|)|M72[T]?|M73[T]?|M75[GT]?|M77G|M79T|M7L|M7LN|M81|M810|M81T|M82|M92|M92KS|M92S|M717G|M721|M722G|M723|M725G|M739|M785|M791|M92SK|M93D)(?: Build|\\) AppleWebKit)', None, 'Aoson $1', 'Aoson', '$1'),
    DeviceMatcher('; {0,2}Aoson ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', 'Aoson $1', 'Aoson', '$1'),
    DeviceMatcher('; {0,2}[Aa]panda[ _\\-]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Apanda $1', 'Apanda', '$1'),
    DeviceMatcher('; {0,2}(?:ARCHOS|Archos) ?(GAMEPAD.{0,200}?)(?: Build|\\) AppleWebKit)', None, 'Archos $1', 'Archos', '$1'),
    DeviceMatcher('ARCHOS; GOGI; ([^;]{1,200});', None, 'Archos $1', 'Archos', '$1'),
    DeviceMatcher('(?:ARCHOS|Archos)[ _]?(.{0,200}?)(?: Build|[;/\\(\\)\\-]|$)', None, 'Archos $1', 'Archos', '$1'),
    DeviceMatcher('; {0,2}(AN(?:7|8|9|10|13)[A-Z0-9]{1,4})(?: Build|\\) AppleWebKit)', None, 'Archos $1', 'Archos', '$1'),
    DeviceMatcher('; {0,2}(A28|A32|A43|A70(?:BHT|CHT|HB|S|X)|A101(?:B|C|IT)|A7EB|A7EB-WK|101G9|80G9)(?: Build|\\) AppleWebKit)', None, 'Archos $1', 'Archos', '$1'),
    DeviceMatcher('; {0,2}(PAD-FMD[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Arival', '$1'),
    DeviceMatcher('; {0,2}(BioniQ) ?([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Arival', '$1 $2'),
    DeviceMatcher('; {0,2}(AN\\d[^;/]{1,100}|ARCHM\\d+)(?: Build|\\) AppleWebKit)', None, 'Arnova $1', 'Arnova', '$1'),
    DeviceMatcher('; {0,2}(?:ARNOVA|Arnova) ?([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Arnova $1', 'Arnova', '$1'),
    DeviceMatcher('; {0,2}(?:ASSISTANT |)(AP)-?([1789]\\d{2}[A-Z]{0,2}|80104)(?: Build|\\) AppleWebKit)', None, 'Assistant $1-$2', 'Assistant', '$1-$2'),
    DeviceMatcher('; {0,2}(ME17\\d[^;/]*|ME3\\d{2}[^;/]{1,100}|K00[A-Z]|Nexus 10|Nexus 7(?: 2013|)|PadFone[^;/]*|Transformer[^;/]*|TF\\d{3}[^;/]*|eeepc)(?: Build|\\) AppleWebKit)', None, 'Asus $1', 'Asus', '$1'),
    DeviceMatcher('; {0,2}ASUS[ _]{0,10}([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Asus $1', 'Asus', '$1'),
    DeviceMatcher('; {0,2}Garmin-Asus ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Garmin-Asus $1', 'Garmin-Asus', '$1'),
    DeviceMatcher('; {0,2}(Garminfone)(?: Build|\\) AppleWebKit)', None, 'Garmin $1', 'Garmin-Asus', '$1'),
    DeviceMatcher('; (@TAB-[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Attab', '$1'),
    DeviceMatcher('; {0,2}(T-(?:07|[^0]\\d)[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Audiosonic', '$1'),
    DeviceMatcher('; {0,2}(?:Axioo[ _\\-]([^;/]{1,100}?)|(picopad)[ _\\-]([^;/]{1,100}?))(?: Build|\\) AppleWebKit)', 'i', 'Axioo $1$2 $3', 'Axioo', '$1$2 $3'),
    DeviceMatcher('; {0,2}(V(?:100|700|800)[^;/]*)(?: Build|\\) AppleWebKit)', None, '$1', 'Azend', '$1'),
    DeviceMatcher('; {0,2}(IBAK\\-[^;/]*)(?: Build|\\) AppleWebKit)', 'i', '$1', 'Bak', '$1'),
    DeviceMatcher('; {0,2}(HY5001|HY6501|X12|X21|I5)(?: Build|\\) AppleWebKit)', None, 'Bedove $1', 'Bedove', '$1'),
    DeviceMatcher('; {0,2}(JC-[^;/]*)(?: Build|\\) AppleWebKit)', None, 'Benss $1', 'Benss', '$1'),
    DeviceMatcher('; {0,2}(BB) ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Blackberry', '$2'),
    DeviceMatcher('; {0,2}(BlackBird)[ _](I8.{0,200}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('; {0,2}(BlackBird)[ _](.{0,200}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('; {0,2}([0-9]+BP[EM][^;/]*|Endeavour[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Blaupunkt $1', 'Blaupunkt', '$1'),
    DeviceMatcher('; {0,2}((?:BLU|Blu)[ _\\-])([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Blu', '$2'),
    DeviceMatcher('; {0,2}(?:BMOBILE )?(Blu|BLU|DASH [^;/]{1,100}|VIVO 4\\.3|TANK 4\\.5)(?: Build|\\) AppleWebKit)', None, '$1', 'Blu', '$1'),
    DeviceMatcher('; {0,2}(TOUCH\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Blusens', '$1'),
    DeviceMatcher('; {0,2}(AX5\\d+)(?: Build|\\) AppleWebKit)', None, '$1', 'Bmobile', '$1'),
    DeviceMatcher('; {0,2}([Bb]q) ([^;/]{1,100}?);?(?: Build|\\) AppleWebKit)', None, '$1 $2', 'bq', '$2'),
    DeviceMatcher('; {0,2}(Maxwell [^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'bq', '$1'),
    DeviceMatcher('; {0,2}((?:B-Tab|B-TAB) ?\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Braun', '$1'),
    DeviceMatcher('; {0,2}(Broncho) ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('; {0,2}CAPTIVA ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Captiva $1', 'Captiva', '$1'),
    DeviceMatcher('; {0,2}(C771|CAL21|IS11CA)(?: Build|\\) AppleWebKit)', None, '$1', 'Casio', '$1'),
    DeviceMatcher('; {0,2}(?:Cat|CAT) ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Cat $1', 'Cat', '$1'),
    DeviceMatcher('; {0,2}(?:Cat)(Nova.{0,200}?)(?: Build|\\) AppleWebKit)', None, 'Cat $1', 'Cat', '$1'),
    DeviceMatcher('; {0,2}(INM8002KP|ADM8000KP_[AB])(?: Build|\\) AppleWebKit)', None, '$1', 'Cat', 'Tablet PHOENIX 8.1J0'),
    DeviceMatcher('; {0,2}(?:[Cc]elkon[ _\\*]|CELKON[ _\\*])([^;/\\)]+) ?(?:Build|;|\\))', None, '$1', 'Celkon', '$1'),
    DeviceMatcher('Build/(?:[Cc]elkon)+_?([^;/_\\)]+)', None, '$1', 'Celkon', '$1'),
    DeviceMatcher('; {0,2}(CT)-?(\\d+)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Celkon', '$1$2'),
    DeviceMatcher('; {0,2}(A19|A19Q|A105|A107[^;/\\)]*) ?(?:Build|;|\\))', None, '$1', 'Celkon', '$1'),
    DeviceMatcher('; {0,2}(TPC[0-9]{4,5})(?: Build|\\) AppleWebKit)', None, '$1', 'ChangJia', '$1'),
    DeviceMatcher('; {0,2}(Cloudfone)[ _](Excite)([^ ][^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2 $3', 'Cloudfone', '$1 $2 $3'),
    DeviceMatcher('; {0,2}(Excite|ICE)[ _](\\d+[^;/]{0,100}?)(?: Build|\\) AppleWebKit)', None, 'Cloudfone $1 $2', 'Cloudfone', 'Cloudfone $1 $2'),
    DeviceMatcher('; {0,2}(Cloudfone|CloudPad)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Cloudfone', '$1 $2'),
    DeviceMatcher('; {0,2}((?:Aquila|Clanga|Rapax)[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', '$1', 'Cmx', '$1'),
    DeviceMatcher('; {0,2}(?:CFW-|Kyros )?(MID[0-9]{4}(?:[ABC]|SR|TV)?)(\\(3G\\)-4G| GB 8K| 3G| 8K| GB)? {0,2}(?:Build|[;\\)])', None, 'CobyKyros $1$2', 'CobyKyros', '$1$2'),
    DeviceMatcher('; {0,2}([^;/]{0,50})Coolpad[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Coolpad', '$1$2'),
    DeviceMatcher('; {0,2}(CUBE[ _])?([KU][0-9]+ ?GT.{0,200}?|A5300)(?: Build|\\) AppleWebKit)', 'i', '$1$2', 'Cube', '$2'),
    DeviceMatcher('; {0,2}CUBOT ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', '$1', 'Cubot', '$1'),
    DeviceMatcher('; {0,2}(BOBBY)(?: Build|\\) AppleWebKit)', 'i', '$1', 'Cubot', '$1'),
    DeviceMatcher('; {0,2}(Dslide [^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Danew', '$1'),
    DeviceMatcher('; {0,2}(XCD)[ _]?(28|35)(?: Build|\\) AppleWebKit)', None, 'Dell $1$2', 'Dell', '$1$2'),
    DeviceMatcher('; {0,2}(001DL)(?: Build|\\) AppleWebKit)', None, 'Dell $1', 'Dell', 'Streak'),
    DeviceMatcher('; {0,2}(?:Dell|DELL) (Streak)(?: Build|\\) AppleWebKit)', None, 'Dell $1', 'Dell', 'Streak'),
    DeviceMatcher('; {0,2}(101DL|GS01|Streak Pro[^;/]{0,100})(?: Build|\\) AppleWebKit)', None, 'Dell $1', 'Dell', 'Streak Pro'),
    DeviceMatcher('; {0,2}([Ss]treak ?7)(?: Build|\\) AppleWebKit)', None, 'Dell $1', 'Dell', 'Streak 7'),
    DeviceMatcher('; {0,2}(Mini-3iX)(?: Build|\\) AppleWebKit)', None, 'Dell $1', 'Dell', '$1'),
    DeviceMatcher('; {0,2}(?:Dell|DELL)[ _](Aero|Venue|Thunder|Mini.{0,200}?|Streak[ _]Pro)(?: Build|\\) AppleWebKit)', None, 'Dell $1', 'Dell', '$1'),
    DeviceMatcher('; {0,2}Dell[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Dell $1', 'Dell', '$1'),
    DeviceMatcher('; {0,2}(TA[CD]-\\d+[^;/]{0,100})(?: Build|\\) AppleWebKit)', None, '$1', 'Denver', '$1'),
    DeviceMatcher('; {0,2}(iP[789]\\d{2}(?:-3G)?|IP10\\d{2}(?:-8GB)?)(?: Build|\\) AppleWebKit)', None, '$1', 'Dex', '$1'),
    DeviceMatcher('; {0,2}(AirTab)[ _\\-]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'DNS', '$1 $2'),
    DeviceMatcher('; {0,2}(F\\-\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Fujitsu', '$1'),
    DeviceMatcher('; {0,2}(HT-03A)(?: Build|\\) AppleWebKit)', None, '$1', 'HTC', 'Magic'),
    DeviceMatcher('; {0,2}(HT\\-\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'HTC', '$1'),
    DeviceMatcher('; {0,2}(L\\-\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'LG', '$1'),
    DeviceMatcher('; {0,2}(N\\-\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Nec', '$1'),
    DeviceMatcher('; {0,2}(P\\-\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Panasonic', '$1'),
    DeviceMatcher('; {0,2}(SC\\-\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Samsung', '$1'),
    DeviceMatcher('; {0,2}(SH\\-\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Sharp', '$1'),
    DeviceMatcher('; {0,2}(SO\\-\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'SonyEricsson', '$1'),
    DeviceMatcher('; {0,2}(T\\-0[12][^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Toshiba', '$1'),
    DeviceMatcher('; {0,2}(DOOV)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'DOOV', '$2'),
    DeviceMatcher('; {0,2}(Enot|ENOT)[ -]?([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Enot', '$2'),
    DeviceMatcher('; {0,2}[^;/]{1,100} Build/(?:CROSS|Cross)+[ _\\-]([^\\)]+)', None, 'CROSS $1', 'Evercoss', 'Cross $1'),
    DeviceMatcher('; {0,2}(CROSS|Cross)[ _\\-]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Evercoss', 'Cross $2'),
    DeviceMatcher('; {0,2}Explay[_ ](.{1,200}?)(?:[\\)]| Build)', None, '$1', 'Explay', '$1'),
    DeviceMatcher('; {0,2}(IQ.{0,200}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Fly', '$1'),
    DeviceMatcher('; {0,2}(Fly|FLY)[ _](IQ[^;]{1,100}?|F[34]\\d+[^;]{0,100}?);?(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Fly', '$2'),
    DeviceMatcher('; {0,2}(M532|Q572|FJL21)(?: Build|\\) AppleWebKit)', None, '$1', 'Fujitsu', '$1'),
    DeviceMatcher('; {0,2}(G1)(?: Build|\\) AppleWebKit)', None, '$1', 'Galapad', '$1'),
    DeviceMatcher('; {0,2}(Geeksphone) ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('; {0,2}(G[^F]?FIVE) ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Gfive', '$2'),
    DeviceMatcher('; {0,2}(Gionee)[ _\\-]([^;/]{1,100}?)(?:/[^;/]{1,100}|)(?: Build|\\) AppleWebKit)', 'i', '$1 $2', 'Gionee', '$2'),
    DeviceMatcher('; {0,2}(GN\\d+[A-Z]?|INFINITY_PASSION|Ctrl_V1)(?: Build|\\) AppleWebKit)', None, 'Gionee $1', 'Gionee', '$1'),
    DeviceMatcher('; {0,2}(E3) Build/JOP40D', None, 'Gionee $1', 'Gionee', '$1'),
    DeviceMatcher('\\sGIONEE[-\\s_](\\w*)', 'i', 'Gionee $1', 'Gionee', '$1'),
    DeviceMatcher('; {0,2}((?:FONE|QUANTUM|INSIGNIA) \\d+[^;/]{0,100}|PLAYTAB)(?: Build|\\) AppleWebKit)', None, 'GoClever $1', 'GoClever', '$1'),
    DeviceMatcher('; {0,2}GOCLEVER ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'GoClever $1', 'GoClever', '$1'),
    DeviceMatcher('; {0,2}(Glass \\d+)(?: Build|\\) AppleWebKit)', None, '$1', 'Google', '$1'),
    DeviceMatcher('; {0,2}([g|G]oogle)? (Pixel[ a-zA-z0-9]{1,100});(?: Build|.{0,50}\\) AppleWebKit)', None, '$2', 'Google', '$2'),
    DeviceMatcher('; {0,2}([g|G]oogle)? (Pixel.{0,200}?)(?: Build|\\) AppleWebKit)', None, '$2', 'Google', '$2'),
    DeviceMatcher('; {0,2}(GSmart)[ -]([^/]{1,50})(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Gigabyte', '$1 $2'),
    DeviceMatcher('; {0,2}(imx5[13]_[^/]{1,50})(?: Build|\\) AppleWebKit)', None, 'Freescale $1', 'Freescale', '$1'),
    DeviceMatcher('; {0,2}Haier[ _\\-]([^/]{1,50})(?: Build|\\) AppleWebKit)', None, 'Haier $1', 'Haier', '$1'),
    DeviceMatcher('; {0,2}(PAD1016)(?: Build|\\) AppleWebKit)', None, 'Haipad $1', 'Haipad', '$1'),
    DeviceMatcher('; {0,2}(M701|M7|M8|M9)(?: Build|\\) AppleWebKit)', None, 'Haipad $1', 'Haipad', '$1'),
    DeviceMatcher('; {0,2}(SN\\d+T[^;\\)/]*)(?: Build|[;\\)])', None, 'Hannspree $1', 'Hannspree', '$1'),
    DeviceMatcher('Build/HCL ME Tablet ([^;\\)]{1,3})[\\);]', None, 'HCLme $1', 'HCLme', '$1'),
    DeviceMatcher('; {0,2}([^;\\/]+) Build/HCL', None, 'HCLme $1', 'HCLme', '$1'),
    DeviceMatcher('; {0,2}(MID-?\\d{4}C[EM])(?: Build|\\) AppleWebKit)', None, 'Hena $1', 'Hena', '$1'),
    DeviceMatcher('; {0,2}(EG\\d{2,}|HS-[^;/]{1,100}|MIRA[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Hisense $1', 'Hisense', '$1'),
    DeviceMatcher('; {0,2}(andromax[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', 'Hisense $1', 'Hisense', '$1'),
    DeviceMatcher('; {0,2}(?:AMAZE[ _](S\\d+)|(S\\d+)[ _]AMAZE)(?: Build|\\) AppleWebKit)', None, 'AMAZE $1$2', 'hitech', 'AMAZE $1$2'),
    DeviceMatcher('; {0,2}(PlayBook)(?: Build|\\) AppleWebKit)', None, 'HP $1', 'HP', '$1'),
    DeviceMatcher('; {0,2}HP ([^/]{1,50})(?: Build|\\) AppleWebKit)', None, 'HP $1', 'HP', '$1'),
    DeviceMatcher('; {0,2}([^/]{1,30}_tenderloin)(?: Build|\\) AppleWebKit)', None, 'HP TouchPad', 'HP', 'TouchPad'),
    DeviceMatcher('; {0,2}(HUAWEI |Huawei-|)([UY][^;/]{1,100}) Build/(?:Huawei|HUAWEI)([UY][^\\);]+)\\)', None, '$1$2', 'Huawei', '$2'),
    DeviceMatcher('; {0,2}([^;/]{1,100}) Build[/ ]Huawei(MT1-U06|[A-Z]{1,50}\\d+[^\\);]{1,50})\\)', None, '$1', 'Huawei', '$2'),
    DeviceMatcher('; {0,2}(S7|M860) Build', None, '$1', 'Huawei', '$1'),
    DeviceMatcher('; {0,2}((?:HUAWEI|Huawei)[ \\-]?)(MediaPad) Build', None, '$1$2', 'Huawei', '$2'),
    DeviceMatcher('; {0,2}((?:HUAWEI[ _]?|Huawei[ _]|)Ascend[ _])([^;/]{1,100}) Build', None, '$1$2', 'Huawei', '$2'),
    DeviceMatcher('; {0,2}((?:HUAWEI|Huawei)[ _\\-]?)((?:G700-|MT-)[^;/]{1,100}) Build', None, '$1$2', 'Huawei', '$2'),
    DeviceMatcher('; {0,2}((?:HUAWEI|Huawei)[ _\\-]?)([^;/]{1,100}) Build', None, '$1$2', 'Huawei', '$2'),
    DeviceMatcher('; {0,2}(MediaPad[^;]{1,200}|SpringBoard) Build/Huawei', None, '$1', 'Huawei', '$1'),
    DeviceMatcher('; {0,2}([^;]{1,200}) Build/(?:Huawei|HUAWEI)', None, '$1', 'Huawei', '$1'),
    DeviceMatcher('; {0,2}([Uu])([89]\\d{3}) Build', None, '$1$2', 'Huawei', 'U$2'),
    DeviceMatcher('; {0,2}(?:Ideos |IDEOS )(S7) Build', None, 'Huawei Ideos$1', 'Huawei', 'Ideos$1'),
    DeviceMatcher('; {0,2}(?:Ideos |IDEOS )([^;/]{1,50}\\s{0,5}|\\s{0,5})Build', None, 'Huawei Ideos$1', 'Huawei', 'Ideos$1'),
    DeviceMatcher('; {0,2}(Orange Daytona|Pulse|Pulse Mini|Vodafone 858|C8500|C8600|C8650|C8660|Nexus 6P|ATH-.{1,200}?) Build[/ ]', None, 'Huawei $1', 'Huawei', '$1'),
    DeviceMatcher('; {0,2}((?:[A-Z]{3})\\-L[A-Za0-9]{2})[\\)]', None, 'Huawei $1', 'Huawei', '$1'),
    DeviceMatcher('; {0,2}([^;]{1,200}) Build/(HONOR|Honor)', None, 'Huawei Honor $1', 'Huawei', 'Honor $1'),
    DeviceMatcher('; {0,2}HTC[ _]([^;]{1,200}); Windows Phone', None, 'HTC $1', 'HTC', '$1'),
    DeviceMatcher('; {0,2}(?:HTC[ _/])+([^ _/]+)(?:[/\\\\]1\\.0 | V|/| +)\\d+\\.\\d[\\d\\.]*(?: {0,2}Build|\\))', None, 'HTC $1', 'HTC', '$1'),
    DeviceMatcher('; {0,2}(?:HTC[ _/])+([^ _/]+)(?:[ _/]([^ _/]+)|)(?:[/\\\\]1\\.0 | V|/| +)\\d+\\.\\d[\\d\\.]*(?: {0,2}Build|\\))', None, 'HTC $1 $2', 'HTC', '$1 $2'),
    DeviceMatcher('; {0,2}(?:HTC[ _/])+([^ _/]+)(?:[ _/]([^ _/]+)(?:[ _/]([^ _/]+)|)|)(?:[/\\\\]1\\.0 | V|/| +)\\d+\\.\\d[\\d\\.]*(?: {0,2}Build|\\))', None, 'HTC $1 $2 $3', 'HTC', '$1 $2 $3'),
    DeviceMatcher('; {0,2}(?:HTC[ _/])+([^ _/]+)(?:[ _/]([^ _/]+)(?:[ _/]([^ _/]+)(?:[ _/]([^ _/]+)|)|)|)(?:[/\\\\]1\\.0 | V|/| +)\\d+\\.\\d[\\d\\.]*(?: {0,2}Build|\\))', None, 'HTC $1 $2 $3 $4', 'HTC', '$1 $2 $3 $4'),
    DeviceMatcher('; {0,2}(?:(?:HTC|htc)(?:_blocked|)[ _/])+([^ _/;]+)(?: {0,2}Build|[;\\)]| - )', None, 'HTC $1', 'HTC', '$1'),
    DeviceMatcher('; {0,2}(?:(?:HTC|htc)(?:_blocked|)[ _/])+([^ _/]+)(?:[ _/]([^ _/;\\)]+)|)(?: {0,2}Build|[;\\)]| - )', None, 'HTC $1 $2', 'HTC', '$1 $2'),
    DeviceMatcher('; {0,2}(?:(?:HTC|htc)(?:_blocked|)[ _/])+([^ _/]+)(?:[ _/]([^ _/]+)(?:[ _/]([^ _/;\\)]+)|)|)(?: {0,2}Build|[;\\)]| - )', None, 'HTC $1 $2 $3', 'HTC', '$1 $2 $3'),
    DeviceMatcher('; {0,2}(?:(?:HTC|htc)(?:_blocked|)[ _/])+([^ _/]+)(?:[ _/]([^ _/]+)(?:[ _/]([^ _/]+)(?:[ _/]([^ /;]+)|)|)|)(?: {0,2}Build|[;\\)]| - )', None, 'HTC $1 $2 $3 $4', 'HTC', '$1 $2 $3 $4'),
    DeviceMatcher('HTC Streaming Player [^\\/]{0,30}/[^\\/]{0,10}/ htc_([^/]{1,10}) /', None, 'HTC $1', 'HTC', '$1'),
    DeviceMatcher('(?:[;,] {0,2}|^)(?:htccn_chs-|)HTC[ _-]?([^;]{1,200}?)(?: {0,2}Build|clay|Android|-?Mozilla| Opera| Profile| UNTRUSTED|[;/\\(\\)]|$)', 'i', 'HTC $1', 'HTC', '$1'),
    DeviceMatcher('; {0,2}(A6277|ADR6200|ADR6300|ADR6350|ADR6400[A-Z]*|ADR6425[A-Z]*|APX515CKT|ARIA|Desire[^_ ]*|Dream|EndeavorU|Eris|Evo|Flyer|HD2|Hero|HERO200|Hero CDMA|HTL21|Incredible|Inspire[A-Z0-9]*|Legend|Liberty|Nexus ?(?:One|HD2)|One|One S C2|One[ _]?(?:S|V|X\\+?)\\w*|PC36100|PG06100|PG86100|S31HT|Sensation|Wildfire)(?: Build|[/;\\(\\)])', 'i', 'HTC $1', 'HTC', '$1'),
    DeviceMatcher('; {0,2}(ADR6200|ADR6400L|ADR6425LVW|Amaze|DesireS?|EndeavorU|Eris|EVO|Evo\\d[A-Z]+|HD2|IncredibleS?|Inspire[A-Z0-9]*|Inspire[A-Z0-9]*|Sensation[A-Z0-9]*|Wildfire)[ _-](.{1,200}?)(?:[/;\\)]|Build|MIUI|1\\.0)', 'i', 'HTC $1 $2', 'HTC', '$1 $2'),
    DeviceMatcher('; {0,2}HYUNDAI (T\\d[^/]{0,10})(?: Build|\\) AppleWebKit)', None, 'Hyundai $1', 'Hyundai', '$1'),
    DeviceMatcher('; {0,2}HYUNDAI ([^;/]{1,10}?)(?: Build|\\) AppleWebKit)', None, 'Hyundai $1', 'Hyundai', '$1'),
    DeviceMatcher('; {0,2}(X700|Hold X|MB-6900)(?: Build|\\) AppleWebKit)', None, 'Hyundai $1', 'Hyundai', '$1'),
    DeviceMatcher('; {0,2}(?:iBall[ _\\-]|)(Andi)[ _]?(\\d[^;/]*)(?: Build|\\) AppleWebKit)', 'i', '$1 $2', 'iBall', '$1 $2'),
    DeviceMatcher('; {0,2}(IBall)(?:[ _]([^;/]{1,100}?)|)(?: Build|\\) AppleWebKit)', 'i', '$1 $2', 'iBall', '$2'),
    DeviceMatcher('; {0,2}(NT-\\d+[^ ;/]{0,50}|Net[Tt]AB [^;/]{1,50}|Mercury [A-Z]{1,50}|iconBIT)(?: S/N:[^;/]{1,50}|)(?: Build|\\) AppleWebKit)', None, '$1', 'IconBIT', '$1'),
    DeviceMatcher('; {0,2}(IMO)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', '$1 $2', 'IMO', '$2'),
    DeviceMatcher('; {0,2}i-?mobile[ _]([^/]{1,50})(?: Build|\\) AppleWebKit)', 'i', 'i-mobile $1', 'imobile', '$1'),
    DeviceMatcher('; {0,2}(i-(?:style|note)[^/]{0,10})(?: Build|\\) AppleWebKit)', 'i', 'i-mobile $1', 'imobile', '$1'),
    DeviceMatcher('; {0,2}(ImPAD) ?(\\d+(?:.){0,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Impression', '$1 $2'),
    DeviceMatcher('; {0,2}(Infinix)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Infinix', '$2'),
    DeviceMatcher('; {0,2}(Informer)[ \\-]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Informer', '$2'),
    DeviceMatcher('; {0,2}(TAB) ?([78][12]4)(?: Build|\\) AppleWebKit)', None, 'Intenso $1', 'Intenso', '$1 $2'),
    DeviceMatcher('; {0,2}(?:Intex[ _]|)(AQUA|Aqua)([ _\\.\\-])([^;/]{1,100}?) {0,2}(?:Build|;)', None, '$1$2$3', 'Intex', '$1 $3'),
    DeviceMatcher('; {0,2}(?:INTEX|Intex)(?:[_ ]([^\\ _;/]+))(?:[_ ]([^\\ _;/]+)|) {0,2}(?:Build|;)', None, '$1 $2', 'Intex', '$1 $2'),
    DeviceMatcher('; {0,2}([iI]Buddy)[ _]?(Connect)(?:_|\\?_| |)([^;/]{0,50}) {0,2}(?:Build|;)', None, '$1 $2 $3', 'Intex', 'iBuddy $2 $3'),
    DeviceMatcher('; {0,2}(I-Buddy)[ _]([^;/]{1,100}?) {0,2}(?:Build|;)', None, '$1 $2', 'Intex', 'iBuddy $2'),
    DeviceMatcher('; {0,2}(iOCEAN) ([^/]{1,50})(?: Build|\\) AppleWebKit)', 'i', '$1 $2', 'iOCEAN', '$2'),
    DeviceMatcher('; {0,2}(TP\\d+(?:\\.\\d+|)\\-\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'ionik $1', 'ionik', '$1'),
    DeviceMatcher('; {0,2}(M702pro)(?: Build|\\) AppleWebKit)', None, '$1', 'Iru', '$1'),
    DeviceMatcher('; {0,2}itel ([^;/]*)(?: Build|\\) AppleWebKit)', None, 'Itel $1', 'Itel', '$1'),
    DeviceMatcher('; {0,2}(DE88Plus|MD70)(?: Build|\\) AppleWebKit)', None, '$1', 'Ivio', '$1'),
    DeviceMatcher('; {0,2}IVIO[_\\-]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Ivio', '$1'),
    DeviceMatcher('; {0,2}(TPC-\\d+|JAY-TECH)(?: Build|\\) AppleWebKit)', None, '$1', 'Jaytech', '$1'),
    DeviceMatcher('; {0,2}(JY-[^;/]{1,100}|G[234]S?)(?: Build|\\) AppleWebKit)', None, '$1', 'Jiayu', '$1'),
    DeviceMatcher('; {0,2}(JXD)[ _\\-]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'JXD', '$2'),
    DeviceMatcher('; {0,2}Karbonn[ _]?([^;/]{1,100}) {0,2}(?:Build|;)', 'i', '$1', 'Karbonn', '$1'),
    DeviceMatcher('; {0,2}([^;]{1,200}) Build/Karbonn', None, '$1', 'Karbonn', '$1'),
    DeviceMatcher('; {0,2}(A11|A39|A37|A34|ST8|ST10|ST7|Smart Tab3|Smart Tab2|Titanium S\\d) +Build', None, '$1', 'Karbonn', '$1'),
    DeviceMatcher('; {0,2}(IS01|IS03|IS05|IS\\d{2}SH)(?: Build|\\) AppleWebKit)', None, '$1', 'Sharp', '$1'),
    DeviceMatcher('; {0,2}(IS04)(?: Build|\\) AppleWebKit)', None, '$1', 'Regza', '$1'),
    DeviceMatcher('; {0,2}(IS06|IS\\d{2}PT)(?: Build|\\) AppleWebKit)', None, '$1', 'Pantech', '$1'),
    DeviceMatcher('; {0,2}(IS11S)(?: Build|\\) AppleWebKit)', None, '$1', 'SonyEricsson', 'Xperia Acro'),
    DeviceMatcher('; {0,2}(IS11CA)(?: Build|\\) AppleWebKit)', None, '$1', 'Casio', 'GzOne $1'),
    DeviceMatcher('; {0,2}(IS11LG)(?: Build|\\) AppleWebKit)', None, '$1', 'LG', 'Optimus X'),
    DeviceMatcher('; {0,2}(IS11N)(?: Build|\\) AppleWebKit)', None, '$1', 'Medias', '$1'),
    DeviceMatcher('; {0,2}(IS11PT)(?: Build|\\) AppleWebKit)', None, '$1', 'Pantech', 'MIRACH'),
    DeviceMatcher('; {0,2}(IS12F)(?: Build|\\) AppleWebKit)', None, '$1', 'Fujitsu', 'Arrows ES'),
    DeviceMatcher('; {0,2}(IS12M)(?: Build|\\) AppleWebKit)', None, '$1', 'Motorola', 'XT909'),
    DeviceMatcher('; {0,2}(IS12S)(?: Build|\\) AppleWebKit)', None, '$1', 'SonyEricsson', 'Xperia Acro HD'),
    DeviceMatcher('; {0,2}(ISW11F)(?: Build|\\) AppleWebKit)', None, '$1', 'Fujitsu', 'Arrowz Z'),
    DeviceMatcher('; {0,2}(ISW11HT)(?: Build|\\) AppleWebKit)', None, '$1', 'HTC', 'EVO'),
    DeviceMatcher('; {0,2}(ISW11K)(?: Build|\\) AppleWebKit)', None, '$1', 'Kyocera', 'DIGNO'),
    DeviceMatcher('; {0,2}(ISW11M)(?: Build|\\) AppleWebKit)', None, '$1', 'Motorola', 'Photon'),
    DeviceMatcher('; {0,2}(ISW11SC)(?: Build|\\) AppleWebKit)', None, '$1', 'Samsung', 'GALAXY S II WiMAX'),
    DeviceMatcher('; {0,2}(ISW12HT)(?: Build|\\) AppleWebKit)', None, '$1', 'HTC', 'EVO 3D'),
    DeviceMatcher('; {0,2}(ISW13HT)(?: Build|\\) AppleWebKit)', None, '$1', 'HTC', 'J'),
    DeviceMatcher('; {0,2}(ISW?[0-9]{2}[A-Z]{0,2})(?: Build|\\) AppleWebKit)', None, '$1', 'KDDI', '$1'),
    DeviceMatcher('; {0,2}(INFOBAR [^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'KDDI', '$1'),
    DeviceMatcher('; {0,2}(JOYPAD|Joypad)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Kingcom', '$1 $2'),
    DeviceMatcher('; {0,2}(Vox|VOX|Arc|K080)(?: Build|\\) AppleWebKit)', 'i', '$1', 'Kobo', '$1'),
    DeviceMatcher('\\b(Kobo Touch)\\b', None, '$1', 'Kobo', '$1'),
    DeviceMatcher('; {0,2}(K-Touch)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', '$1 $2', 'Ktouch', '$2'),
    DeviceMatcher('; {0,2}((?:EV|KM)-S\\d+[A-Z]?)(?: Build|\\) AppleWebKit)', 'i', '$1', 'KTtech', '$1'),
    DeviceMatcher('; {0,2}(Zio|Hydro|Torque|Event|EVENT|Echo|Milano|Rise|URBANO PROGRESSO|WX04K|WX06K|WX10K|KYL21|101K|C5[12]\\d{2})(?: Build|\\) AppleWebKit)', None, '$1', 'Kyocera', '$1'),
    DeviceMatcher('; {0,2}(?:LAVA[ _]|)IRIS[ _\\-]?([^/;\\)]+) {0,2}(?:;|\\)|Build)', 'i', 'Iris $1', 'Lava', 'Iris $1'),
    DeviceMatcher('; {0,2}LAVA[ _]([^;/]{1,100}) Build', None, '$1', 'Lava', '$1'),
    DeviceMatcher('; {0,2}(?:(Aspire A1)|(?:LEMON|Lemon)[ _]([^;/]{1,100}))_?(?: Build|\\) AppleWebKit)', None, 'Lemon $1$2', 'Lemon', '$1$2'),
    DeviceMatcher('; {0,2}(TAB-1012)(?: Build|\\) AppleWebKit)', None, 'Lenco $1', 'Lenco', '$1'),
    DeviceMatcher('; Lenco ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Lenco $1', 'Lenco', '$1'),
    DeviceMatcher('; {0,2}(A1_07|A2107A-H|S2005A-H|S1-37AH0) Build', None, '$1', 'Lenovo', '$1'),
    DeviceMatcher('; {0,2}(Idea[Tp]ab)[ _]([^;/]{1,100});? Build', None, 'Lenovo $1 $2', 'Lenovo', '$1 $2'),
    DeviceMatcher('; {0,2}(Idea(?:Tab|pad)) ?([^;/]{1,100}) Build', None, 'Lenovo $1 $2', 'Lenovo', '$1 $2'),
    DeviceMatcher('; {0,2}(ThinkPad) ?(Tablet) Build/', None, 'Lenovo $1 $2', 'Lenovo', '$1 $2'),
    DeviceMatcher('; {0,2}(?:LNV-|)(?:=?[Ll]enovo[ _\\-]?|LENOVO[ _])(.{1,200}?)(?:Build|[;/\\)])', None, 'Lenovo $1', 'Lenovo', '$1'),
    DeviceMatcher('[;,] (?:Vodafone |)(SmartTab) ?(II) ?(\\d+) Build/', None, 'Lenovo $1 $2 $3', 'Lenovo', '$1 $2 $3'),
    DeviceMatcher('; {0,2}(?:Ideapad |)K1 Build/', None, 'Lenovo Ideapad K1', 'Lenovo', 'Ideapad K1'),
    DeviceMatcher('; {0,2}(3GC101|3GW10[01]|A390) Build/', None, '$1', 'Lenovo', '$1'),
    DeviceMatcher('\\b(?:Lenovo|LENOVO)+[ _\\-]?([^,;:/ ]+)', None, 'Lenovo $1', 'Lenovo', '$1'),
    DeviceMatcher('; {0,2}(MFC\\d+)[A-Z]{2}([^;,/]*),?(?: Build|\\) AppleWebKit)', None, '$1$2', 'Lexibook', '$1$2'),
    DeviceMatcher('; {0,2}(E[34][0-9]{2}|LS[6-8][0-9]{2}|VS[6-9][0-9]+[^;/]{1,30}|Nexus 4|Nexus 5X?|GT540f?|Optimus (?:2X|G|4X HD)|OptimusX4HD) {0,2}(?:Build|;)', None, '$1', 'LG', '$1'),
    DeviceMatcher('[;:] {0,2}(L-\\d+[A-Z]|LGL\\d+[A-Z]?)(?:/V\\d+|) {0,2}(?:Build|[;\\)])', None, '$1', 'LG', '$1'),
    DeviceMatcher('; {0,2}(LG-)([A-Z]{1,2}\\d{2,}[^,;/\\)\\(]*?)(?:Build| V\\d+|[,;/\\)\\(]|$)', None, '$1$2', 'LG', '$2'),
    DeviceMatcher('; {0,2}(LG[ \\-]|LG)([^;/]{1,100})[;/]? Build', None, '$1$2', 'LG', '$2'),
    DeviceMatcher('^(LG)-([^;/]{1,100})/ Mozilla/.{0,200}; Android', None, '$1 $2', 'LG', '$2'),
    DeviceMatcher('(Web0S); Linux/(SmartTV)', None, 'LG $1 $2', 'LG', '$1 $2'),
    DeviceMatcher('; {0,2}((?:SMB|smb)[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Malata', '$1'),
    DeviceMatcher('; {0,2}(?:Malata|MALATA) ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Malata', '$1'),
    DeviceMatcher('; {0,2}(MS[45][0-9]{3}|MID0[568][NS]?|MID[1-9]|MID[78]0[1-9]|MID970[1-9]|MID100[1-9])(?: Build|\\) AppleWebKit)', None, '$1', 'Manta', '$1'),
    DeviceMatcher('; {0,2}(M1052|M806|M9000|M9100|M9701|MID100|MID120|MID125|MID130|MID135|MID140|MID701|MID710|MID713|MID727|MID728|MID731|MID732|MID733|MID735|MID736|MID737|MID760|MID800|MID810|MID820|MID830|MID833|MID835|MID860|MID900|MID930|MID933|MID960|MID980)(?: Build|\\) AppleWebKit)', None, '$1', 'Match', '$1'),
    DeviceMatcher('; {0,2}(GenxDroid7|MSD7.{0,200}?|AX\\d.{0,200}?|Tab 701|Tab 722)(?: Build|\\) AppleWebKit)', None, 'Maxx $1', 'Maxx', '$1'),
    DeviceMatcher('; {0,2}(M-PP[^;/]{1,30}|PhonePad ?\\d{2,}[^;/]{1,30}?)(?: Build|\\) AppleWebKit)', None, 'Mediacom $1', 'Mediacom', '$1'),
    DeviceMatcher('; {0,2}(M-MP[^;/]{1,30}|SmartPad ?\\d{2,}[^;/]{1,30}?)(?: Build|\\) AppleWebKit)', None, 'Mediacom $1', 'Mediacom', '$1'),
    DeviceMatcher('; {0,2}(?:MD_|)LIFETAB[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', 'Medion Lifetab $1', 'Medion', 'Lifetab $1'),
    DeviceMatcher('; {0,2}MEDION ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Medion $1', 'Medion', '$1'),
    DeviceMatcher('; {0,2}(M030|M031|M035|M040|M065|m9)(?: Build|\\) AppleWebKit)', None, 'Meizu $1', 'Meizu', '$1'),
    DeviceMatcher('; {0,2}(?:meizu_|MEIZU )(.{1,200}?) {0,2}(?:Build|[;\\)])', None, 'Meizu $1', 'Meizu', '$1'),
    DeviceMatcher('Quest 2', None, 'Quest', 'Meta', 'Quest 2'),
    DeviceMatcher('Quest Pro', None, 'Quest', 'Meta', 'Quest Pro'),
    DeviceMatcher('Quest', None, 'Quest', 'Meta', 'Quest'),
    DeviceMatcher('; {0,2}(?:Micromax[ _](A111|A240)|(A111|A240)) Build', 'i', 'Micromax $1$2', 'Micromax', '$1$2'),
    DeviceMatcher('; {0,2}Micromax[ _](A\\d{2,3}[^;/]*) Build', 'i', 'Micromax $1', 'Micromax', '$1'),
    DeviceMatcher('; {0,2}(A\\d{2}|A[12]\\d{2}|A90S|A110Q) Build', 'i', 'Micromax $1', 'Micromax', '$1'),
    DeviceMatcher('; {0,2}Micromax[ _](P\\d{3}[^;/]*) Build', 'i', 'Micromax $1', 'Micromax', '$1'),
    DeviceMatcher('; {0,2}(P\\d{3}|P\\d{3}\\(Funbook\\)) Build', 'i', 'Micromax $1', 'Micromax', '$1'),
    DeviceMatcher('; {0,2}(MITO)[ _\\-]?([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', '$1 $2', 'Mito', '$2'),
    DeviceMatcher('; {0,2}(Cynus)[ _](F5|T\\d|.{1,200}?) {0,2}(?:Build|[;/\\)])', 'i', '$1 $2', 'Mobistel', '$1 $2'),
    DeviceMatcher('; {0,2}(MODECOM |)(FreeTab) ?([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', '$1$2 $3', 'Modecom', '$2 $3'),
    DeviceMatcher('; {0,2}(MODECOM )([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', '$1 $2', 'Modecom', '$2'),
    DeviceMatcher('; {0,2}(MZ\\d{3}\\+?|MZ\\d{3} 4G|Xoom|XOOM[^;/]*) Build', None, 'Motorola $1', 'Motorola', '$1'),
    DeviceMatcher('; {0,2}(Milestone )(XT[^;/]*) Build', None, 'Motorola $1$2', 'Motorola', '$2'),
    DeviceMatcher('; {0,2}(Motoroi ?x|Droid X|DROIDX) Build', 'i', 'Motorola $1', 'Motorola', 'DROID X'),
    DeviceMatcher('; {0,2}(Droid[^;/]*|DROID[^;/]*|Milestone[^;/]*|Photon|Triumph|Devour|Titanium) Build', None, 'Motorola $1', 'Motorola', '$1'),
    DeviceMatcher('; {0,2}(A555|A85[34][^;/]*|A95[356]|ME[58]\\d{2}\\+?|ME600|ME632|ME722|MB\\d{3}\\+?|MT680|MT710|MT870|MT887|MT917|WX435|WX453|WX44[25]|XT\\d{3,4}[A-Z\\+]*|CL[iI]Q|CL[iI]Q XT) Build', None, '$1', 'Motorola', '$1'),
    DeviceMatcher('; {0,2}(Motorola MOT-|Motorola[ _\\-]|MOT\\-?)([^;/]{1,100}) Build', None, '$1$2', 'Motorola', '$2'),
    DeviceMatcher('; {0,2}(Moto[_ ]?|MOT\\-)([^;/]{1,100}) Build', None, '$1$2', 'Motorola', '$2'),
    DeviceMatcher('; {0,2}((?:MP[DQ]C|MPG\\d{1,4}|MP\\d{3,4}|MID(?:(?:10[234]|114|43|7[247]|8[24]|7)C|8[01]1))[^;/]*)(?: Build|\\) AppleWebKit)', None, '$1', 'Mpman', '$1'),
    DeviceMatcher('; {0,2}(?:MSI[ _]|)(Primo\\d+|Enjoy[ _\\-][^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', '$1', 'Msi', '$1'),
    DeviceMatcher('; {0,2}Multilaser[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Multilaser', '$1'),
    DeviceMatcher('; {0,2}(My)[_]?(Pad)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1$2 $3', 'MyPhone', '$1$2 $3'),
    DeviceMatcher('; {0,2}(My)\\|?(Phone)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1$2 $3', 'MyPhone', '$3'),
    DeviceMatcher('; {0,2}(A\\d+)[ _](Duo|)(?: Build|\\) AppleWebKit)', 'i', '$1 $2', 'MyPhone', '$1 $2'),
    DeviceMatcher('; {0,2}(myTab[^;/]*)(?: Build|\\) AppleWebKit)', None, '$1', 'Mytab', '$1'),
    DeviceMatcher('; {0,2}(NABI2?-)([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Nabi', '$2'),
    DeviceMatcher('; {0,2}(N-\\d+[CDE])(?: Build|\\) AppleWebKit)', None, '$1', 'Nec', '$1'),
    DeviceMatcher('; ?(NEC-)(.{0,200}?)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Nec', '$2'),
    DeviceMatcher('; {0,2}(LT-NA7)(?: Build|\\) AppleWebKit)', None, '$1', 'Nec', 'Lifetouch Note'),
    DeviceMatcher('; {0,2}(NXM\\d+[A-Za-z0-9_]{0,50}|Next\\d[A-Za-z0-9_ \\-]{0,50}|NEXT\\d[A-Za-z0-9_ \\-]{0,50}|Nextbook [A-Za-z0-9_ ]{0,50}|DATAM803HC|M805)(?: Build|[\\);])', None, '$1', 'Nextbook', '$1'),
    DeviceMatcher('; {0,2}(Nokia)([ _\\-]{0,5})([^;/]{0,50}) Build', 'i', '$1$2$3', 'Nokia', '$3'),
    DeviceMatcher('; {0,2}(TA\\-\\d{4})(?: Build|\\) AppleWebKit)', None, 'Nokia $1', 'Nokia', '$1'),
    DeviceMatcher('; {0,2}(Nook ?|Barnes & Noble Nook |BN )([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Nook', '$2'),
    DeviceMatcher('; {0,2}(NOOK |)(BNRV200|BNRV200A|BNTV250|BNTV250A|BNTV400|BNTV600|LogicPD Zoom2)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Nook', '$2'),
    DeviceMatcher('; Build/(Nook)', None, '$1', 'Nook', 'Tablet'),
    DeviceMatcher('; {0,2}(OP110|OliPad[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Olivetti $1', 'Olivetti', '$1'),
    DeviceMatcher('; {0,2}OMEGA[ _\\-](MID[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Omega $1', 'Omega', '$1'),
    DeviceMatcher('^(MID7500|MID\\d+) Mozilla/5\\.0 \\(iPad;', None, 'Omega $1', 'Omega', '$1'),
    DeviceMatcher('; {0,2}((?:CIUS|cius)[^;/]*)(?: Build|\\) AppleWebKit)', None, 'Openpeak $1', 'Openpeak', '$1'),
    DeviceMatcher('; {0,2}(Find ?(?:5|7a)|R8[012]\\d{1,2}|T703\\d?|U70\\d{1,2}T?|X90\\d{1,2}|[AFR]\\d{1,2}[a-z]{1,2})(?: Build|\\) AppleWebKit)', None, 'Oppo $1', 'Oppo', '$1'),
    DeviceMatcher('; {0,2}OPPO ?([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Oppo $1', 'Oppo', '$1'),
    DeviceMatcher('; {0,2}(CPH\\d{1,4}|RMX\\d{1,4}|P[A-Z]{3}\\d{2})(?: Build|\\) AppleWebKit)', None, 'Oppo $1', 'Oppo'),
    DeviceMatcher('; {0,2}(A1601)(?: Build|\\) AppleWebKit)', None, 'Oppo F1s', 'Oppo', '$1'),
    DeviceMatcher('; {0,2}(?:Odys\\-|ODYS\\-|ODYS )([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Odys $1', 'Odys', '$1'),
    DeviceMatcher('; {0,2}(SELECT) ?(7)(?: Build|\\) AppleWebKit)', None, 'Odys $1 $2', 'Odys', '$1 $2'),
    DeviceMatcher('; {0,2}(PEDI)_(PLUS)_(W)(?: Build|\\) AppleWebKit)', None, 'Odys $1 $2 $3', 'Odys', '$1 $2 $3'),
    DeviceMatcher('; {0,2}(AEON|BRAVIO|FUSION|FUSION2IN1|Genio|EOS10|IEOS[^;/]*|IRON|Loox|LOOX|LOOX Plus|Motion|NOON|NOON_PRO|NEXT|OPOS|PEDI[^;/]*|PRIME[^;/]*|STUDYTAB|TABLO|Tablet-PC-4|UNO_X8|XELIO[^;/]*|Xelio ?\\d+ ?[Pp]ro|XENO10|XPRESS PRO)(?: Build|\\) AppleWebKit)', None, 'Odys $1', 'Odys', '$1'),
    DeviceMatcher('; (ONE [a-zA-Z]\\d+)(?: Build|\\) AppleWebKit)', None, 'OnePlus $1', 'OnePlus', '$1'),
    DeviceMatcher('; (ONEPLUS [a-zA-Z]\\d+)(?: Build|\\) AppleWebKit)', None, 'OnePlus $1', 'OnePlus', '$1'),
    DeviceMatcher('; {0,2}(HD1903|GM1917|IN2025|LE2115|LE2127|HD1907|BE2012|BE2025|BE2026|BE2028|BE2029|DE2117|DE2118|EB2101|GM1900|GM1910|GM1915|HD1905|HD1925|IN2015|IN2017|IN2019|KB2005|KB2007|LE2117|LE2125|BE2015|GM1903|HD1900|HD1901|HD1910|HD1913|IN2010|IN2013|IN2020|LE2111|LE2120|LE2121|LE2123|BE2011|IN2023|KB2003|LE2113|NE2215|DN2101)(?: Build|\\) AppleWebKit)', None, 'OnePlus $1', 'OnePlus', 'OnePlus $1'),
    DeviceMatcher('; (OnePlus[ a-zA-z0-9]{0,50});((?: Build|.{0,50}\\) AppleWebKit))', None, '$1', 'OnePlus', '$1'),
    DeviceMatcher('; (OnePlus[ a-zA-z0-9]{0,50})((?: Build|\\) AppleWebKit))', None, '$1', 'OnePlus', '$1'),
    DeviceMatcher('; {0,2}(TP-\\d+)(?: Build|\\) AppleWebKit)', None, 'Orion $1', 'Orion', '$1'),
    DeviceMatcher('; {0,2}(G100W?)(?: Build|\\) AppleWebKit)', None, 'PackardBell $1', 'PackardBell', '$1'),
    DeviceMatcher('; {0,2}(Panasonic)[_ ]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('; {0,2}(FZ-A1B|JT-B1)(?: Build|\\) AppleWebKit)', None, 'Panasonic $1', 'Panasonic', '$1'),
    DeviceMatcher('; {0,2}(dL1|DL1)(?: Build|\\) AppleWebKit)', None, 'Panasonic $1', 'Panasonic', '$1'),
    DeviceMatcher('; {0,2}(SKY[ _]|)(IM\\-[AT]\\d{3}[^;/]{1,100}).{0,30} Build/', None, 'Pantech $1$2', 'Pantech', '$1$2'),
    DeviceMatcher('; {0,2}((?:ADR8995|ADR910L|ADR930L|ADR930VW|PTL21|P8000)(?: 4G|)) Build/', None, '$1', 'Pantech', '$1'),
    DeviceMatcher('; {0,2}Pantech([^;/]{1,30}).{0,200}? Build/', None, 'Pantech $1', 'Pantech', '$1'),
    DeviceMatcher('; {0,2}(papyre)[ _\\-]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', '$1 $2', 'Papyre', '$2'),
    DeviceMatcher('; {0,2}(?:Touchlet )?(X10\\.[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Pearl $1', 'Pearl', '$1'),
    DeviceMatcher('; PHICOMM (i800)(?: Build|\\) AppleWebKit)', None, 'Phicomm $1', 'Phicomm', '$1'),
    DeviceMatcher('; PHICOMM ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Phicomm $1', 'Phicomm', '$1'),
    DeviceMatcher('; {0,2}(FWS\\d{3}[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Phicomm $1', 'Phicomm', '$1'),
    DeviceMatcher('; {0,2}(D633|D822|D833|T539|T939|V726|W335|W336|W337|W3568|W536|W5510|W626|W632|W6350|W6360|W6500|W732|W736|W737|W7376|W820|W832|W8355|W8500|W8510|W930)(?: Build|\\) AppleWebKit)', None, '$1', 'Philips', '$1'),
    DeviceMatcher('; {0,2}(?:Philips|PHILIPS)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Philips $1', 'Philips', '$1'),
    DeviceMatcher('Android 4\\..{0,200}; {0,2}(M[12356789]|U[12368]|S[123])\\ ?(pro)?(?: Build|\\) AppleWebKit)', None, 'Pipo $1$2', 'Pipo', '$1$2'),
    DeviceMatcher('; {0,2}(MOMO[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Ployer', '$1'),
    DeviceMatcher('; {0,2}(?:Polaroid[ _]|)((?:MIDC\\d{3,}|PMID\\d{2,}|PTAB\\d{3,})[^;/]{0,30}?)(\\/[^;/]{0,30}|)(?: Build|\\) AppleWebKit)', None, '$1', 'Polaroid', '$1'),
    DeviceMatcher('; {0,2}(?:Polaroid )(Tablet)(?: Build|\\) AppleWebKit)', None, '$1', 'Polaroid', '$1'),
    DeviceMatcher('; {0,2}(POMP)[ _\\-](.{1,200}?) {0,2}(?:Build|[;/\\)])', None, '$1 $2', 'Pomp', '$2'),
    DeviceMatcher('; {0,2}(TB07STA|TB10STA|TB07FTA|TB10FTA)(?: Build|\\) AppleWebKit)', None, '$1', 'Positivo', '$1'),
    DeviceMatcher('; {0,2}(?:Positivo |)((?:YPY|Ypy)[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Positivo', '$1'),
    DeviceMatcher('; {0,2}(MOB-[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'POV', '$1'),
    DeviceMatcher('; {0,2}POV[ _\\-]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'POV $1', 'POV', '$1'),
    DeviceMatcher('; {0,2}((?:TAB-PLAYTAB|TAB-PROTAB|PROTAB|PlayTabPro|Mobii[ _\\-]|TAB-P)[^;/]*)(?: Build|\\) AppleWebKit)', None, 'POV $1', 'POV', '$1'),
    DeviceMatcher('; {0,2}(?:Prestigio |)((?:PAP|PMP)\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Prestigio $1', 'Prestigio', '$1'),
    DeviceMatcher('; {0,2}(PLT[0-9]{4}.{0,200}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Proscan', '$1'),
    DeviceMatcher('; {0,2}(A2|A5|A8|A900)_?(Classic|)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Qmobile', '$1 $2'),
    DeviceMatcher('; {0,2}(Q[Mm]obile)_([^_]+)_([^_]+?)(?: Build|\\) AppleWebKit)', None, 'Qmobile $2 $3', 'Qmobile', '$2 $3'),
    DeviceMatcher('; {0,2}(Q\\-?[Mm]obile)[_ ](A[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Qmobile $2', 'Qmobile', '$2'),
    DeviceMatcher('; {0,2}(Q\\-Smart)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Qmobilevn', '$2'),
    DeviceMatcher('; {0,2}(Q\\-?[Mm]obile)[ _\\-](S[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Qmobilevn', '$2'),
    DeviceMatcher('; {0,2}(TA1013)(?: Build|\\) AppleWebKit)', None, '$1', 'Quanta', '$1'),
    DeviceMatcher('; (RCT\\w+)(?: Build|\\) AppleWebKit)', None, '$1', 'RCA', '$1'),
    DeviceMatcher('; RCA (\\w+)(?: Build|\\) AppleWebKit)', None, 'RCA $1', 'RCA', '$1'),
    DeviceMatcher('; {0,2}(RK\\d+),?(?: Build|\\) AppleWebKit)', None, '$1', 'Rockchip', '$1'),
    DeviceMatcher(' Build/(RK\\d+)', None, '$1', 'Rockchip', '$1'),
    DeviceMatcher('; {0,2}(SAMSUNG |Samsung |)((?:Galaxy (?:Note II|S\\d)|GT-I9082|GT-I9205|GT-N7\\d{3}|SM-N9005)[^;/]{0,100})\\/?[^;/]{0,50} Build/', None, 'Samsung $1$2', 'Samsung', '$2'),
    DeviceMatcher('; {0,2}(Google |)(Nexus [Ss](?: 4G|)) Build/', None, 'Samsung $1$2', 'Samsung', '$2'),
    DeviceMatcher('; {0,2}(SAMSUNG |Samsung )([^\\/]{0,50})\\/[^ ]{0,50} Build/', None, 'Samsung $2', 'Samsung', '$2'),
    DeviceMatcher('; {0,2}(Galaxy(?: Ace| Nexus| S ?II+|Nexus S| with MCR 1.2| Mini Plus 4G|)) Build/', None, 'Samsung $1', 'Samsung', '$1'),
    DeviceMatcher('; {0,2}(SAMSUNG[ _\\-]|)(?:SAMSUNG[ _\\-])([^;/]{1,100}) Build', None, 'Samsung $2', 'Samsung', '$2'),
    DeviceMatcher('; {0,2}(SAMSUNG-|)(GT\\-[BINPS]\\d{4}[^\\/]{0,50})(\\/[^ ]{0,50}) Build', None, 'Samsung $1$2$3', 'Samsung', '$2'),
    DeviceMatcher('(?:; {0,2}|^)((?:GT\\-[BIiNPS]\\d{4}|I9\\d{2}0[A-Za-z\\+]?\\b)[^;/\\)]*?)(?:Build|Linux|MIUI|[;/\\)])', None, 'Samsung $1', 'Samsung', '$1'),
    DeviceMatcher('; (SAMSUNG-)([A-Za-z0-9\\-]{0,50}).{0,200} Build/', None, 'Samsung $1$2', 'Samsung', '$2'),
    DeviceMatcher('; {0,2}((?:SCH|SGH|SHV|SHW|SPH|SC|SM)\\-[A-Za-z0-9 ]{1,50})(/?[^ ]*|) Build', None, 'Samsung $1', 'Samsung', '$1'),
    DeviceMatcher('; {0,2}((?:SC)\\-[A-Za-z0-9 ]{1,50})(/?[^ ]*|)\\)', None, 'Samsung $1', 'Samsung', '$1'),
    DeviceMatcher(' ((?:SCH)\\-[A-Za-z0-9 ]{1,50})(/?[^ ]*|) Build', None, 'Samsung $1', 'Samsung', '$1'),
    DeviceMatcher('; {0,2}(Behold ?(?:2|II)|YP\\-G[^;/]{1,100}|EK-GC100|SCL21|I9300) Build', None, 'Samsung $1', 'Samsung', '$1'),
    DeviceMatcher('; {0,2}((?:SCH|SGH|SHV|SHW|SPH|SC|SM)\\-[A-Za-z0-9]{5,6})[\\)]', None, 'Samsung $1', 'Samsung', '$1'),
    DeviceMatcher('; {0,2}(SH\\-?\\d\\d[^;/]{1,100}|SBM\\d[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Sharp', '$1'),
    DeviceMatcher('; {0,2}(SHARP[ -])([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Sharp', '$2'),
    DeviceMatcher('; {0,2}(SPX[_\\-]\\d[^;/]*)(?: Build|\\) AppleWebKit)', None, '$1', 'Simvalley', '$1'),
    DeviceMatcher('; {0,2}(SX7\\-PEARL\\.GmbH)(?: Build|\\) AppleWebKit)', None, '$1', 'Simvalley', '$1'),
    DeviceMatcher('; {0,2}(SP[T]?\\-\\d{2}[^;/]*)(?: Build|\\) AppleWebKit)', None, '$1', 'Simvalley', '$1'),
    DeviceMatcher('; {0,2}(SK\\-.{0,200}?)(?: Build|\\) AppleWebKit)', None, '$1', 'SKtelesys', '$1'),
    DeviceMatcher('; {0,2}(?:SKYTEX|SX)-([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Skytex', '$1'),
    DeviceMatcher('; {0,2}(IMAGINE [^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Skytex', '$1'),
    DeviceMatcher('; {0,2}(SmartQ) ?([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('; {0,2}(WF7C|WF10C|SBT[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Smartbitt', '$1'),
    DeviceMatcher('; {0,2}(SBM(?:003SH|005SH|006SH|007SH|102SH)) Build', None, '$1', 'Sharp', '$1'),
    DeviceMatcher('; {0,2}(003P|101P|101P11C|102P) Build', None, '$1', 'Panasonic', '$1'),
    DeviceMatcher('; {0,2}(00\\dZ) Build/', None, '$1', 'ZTE', '$1'),
    DeviceMatcher('; HTC(X06HT) Build', None, '$1', 'HTC', '$1'),
    DeviceMatcher('; {0,2}(001HT|X06HT) Build', None, '$1', 'HTC', '$1'),
    DeviceMatcher('; {0,2}(201M) Build', None, '$1', 'Motorola', 'XT902'),
    DeviceMatcher('; {0,2}(ST\\d{4}.{0,200})Build/ST', None, 'Trekstor $1', 'Trekstor', '$1'),
    DeviceMatcher('; {0,2}(ST\\d{4}.{0,200}?)(?: Build|\\) AppleWebKit)', None, 'Trekstor $1', 'Trekstor', '$1'),
    DeviceMatcher('; {0,2}(Sony ?Ericsson ?)([^;/]{1,100}) Build', None, '$1$2', 'SonyEricsson', '$2'),
    DeviceMatcher('; {0,2}((?:SK|ST|E|X|LT|MK|MT|WT)\\d{2}[a-z0-9]*(?:-o|)|R800i|U20i) Build', None, '$1', 'SonyEricsson', '$1'),
    DeviceMatcher('; {0,2}(Xperia (?:A8|Arc|Acro|Active|Live with Walkman|Mini|Neo|Play|Pro|Ray|X\\d+)[^;/]{0,50}) Build', 'i', '$1', 'SonyEricsson', '$1'),
    DeviceMatcher('; Sony (Tablet[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Sony $1', 'Sony', '$1'),
    DeviceMatcher('; Sony ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Sony $1', 'Sony', '$1'),
    DeviceMatcher('; {0,2}(Sony)([A-Za-z0-9\\-]+)(?: Build|\\) AppleWebKit)', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('; {0,2}(Xperia [^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Sony', '$1'),
    DeviceMatcher('; {0,2}(C(?:1[0-9]|2[0-9]|53|55|6[0-9])[0-9]{2}|D[25]\\d{3}|D6[56]\\d{2})(?: Build|\\) AppleWebKit)', None, '$1', 'Sony', '$1'),
    DeviceMatcher('; {0,2}(SGP\\d{3}|SGPT\\d{2})(?: Build|\\) AppleWebKit)', None, '$1', 'Sony', '$1'),
    DeviceMatcher('; {0,2}(NW-Z1000Series)(?: Build|\\) AppleWebKit)', None, '$1', 'Sony', '$1'),
    DeviceMatcher('PLAYSTATION 3', None, 'PlayStation 3', 'Sony', 'PlayStation 3'),
    DeviceMatcher('(PlayStation (?:Portable|Vita|\\d+))', None, '$1', 'Sony', '$1'),
    DeviceMatcher('; {0,2}((?:CSL_Spice|Spice|SPICE|CSL)[ _\\-]?|)([Mm][Ii])([ _\\-]|)(\\d{3}[^;/]*)(?: Build|\\) AppleWebKit)', None, '$1$2$3$4', 'Spice', 'Mi$4'),
    DeviceMatcher('; {0,2}(Sprint )(.{1,200}?) {0,2}(?:Build|[;/])', None, '$1$2', 'Sprint', '$2'),
    DeviceMatcher('\\b(Sprint)[: ]([^;,/ ]+)', None, '$1$2', 'Sprint', '$2'),
    DeviceMatcher('; {0,2}(TAGI[ ]?)(MID) ?([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1$2$3', 'Tagi', '$2$3'),
    DeviceMatcher('; {0,2}(Oyster500|Opal 800)(?: Build|\\) AppleWebKit)', None, 'Tecmobile $1', 'Tecmobile', '$1'),
    DeviceMatcher('; {0,2}(TECNO[ _])([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Tecno', '$2'),
    DeviceMatcher('; {0,2}Android for (Telechips|Techvision) ([^ ]+) ', 'i', '$1 $2', '$1', '$2'),
    DeviceMatcher('; {0,2}(T-Hub2)(?: Build|\\) AppleWebKit)', None, '$1', 'Telstra', '$1'),
    DeviceMatcher('; {0,2}(PAD) ?(100[12])(?: Build|\\) AppleWebKit)', None, 'Terra $1$2', 'Terra', '$1$2'),
    DeviceMatcher('; {0,2}(T[BM]-\\d{3}[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Texet', '$1'),
    DeviceMatcher('; {0,2}(tolino [^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Thalia', '$1'),
    DeviceMatcher('; {0,2}Build/.{0,200} (TOLINO_BROWSER)', None, '$1', 'Thalia', 'Tolino Shine'),
    DeviceMatcher('; {0,2}(?:CJ[ -])?(ThL|THL)[ -]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Thl', '$2'),
    DeviceMatcher('; {0,2}(T100|T200|T5|W100|W200|W8s)(?: Build|\\) AppleWebKit)', None, '$1', 'Thl', '$1'),
    DeviceMatcher('; {0,2}(T-Mobile[ _]G2[ _]Touch) Build', None, '$1', 'HTC', 'Hero'),
    DeviceMatcher('; {0,2}(T-Mobile[ _]G2) Build', None, '$1', 'HTC', 'Desire Z'),
    DeviceMatcher('; {0,2}(T-Mobile myTouch Q) Build', None, '$1', 'Huawei', 'U8730'),
    DeviceMatcher('; {0,2}(T-Mobile myTouch) Build', None, '$1', 'Huawei', 'U8680'),
    DeviceMatcher('; {0,2}(T-Mobile_Espresso) Build', None, '$1', 'HTC', 'Espresso'),
    DeviceMatcher('; {0,2}(T-Mobile G1) Build', None, '$1', 'HTC', 'Dream'),
    DeviceMatcher('\\b(T-Mobile ?|)(myTouch)[ _]?([34]G)[ _]?([^\\/]*) (?:Mozilla|Build)', None, '$1$2 $3 $4', 'HTC', '$2 $3 $4'),
    DeviceMatcher('\\b(T-Mobile)_([^_]+)_(.{0,200}) Build', None, '$1 $2 $3', 'Tmobile', '$2 $3'),
    DeviceMatcher('\\b(T-Mobile)[_ ]?(.{0,200}?)Build', None, '$1 $2', 'Tmobile', '$2'),
    DeviceMatcher(' (ATP[0-9]{4})(?: Build|\\) AppleWebKit)', None, '$1', 'Tomtec', '$1'),
    DeviceMatcher(' ?(TOOKY)[ _\\-]([^;/]{1,100}) ?(?:Build|;)', 'i', '$1 $2', 'Tooky', '$2'),
    DeviceMatcher('\\b(TOSHIBA_AC_AND_AZ|TOSHIBA_FOLIO_AND_A|FOLIO_AND_A)', None, '$1', 'Toshiba', 'Folio 100'),
    DeviceMatcher('; {0,2}([Ff]olio ?100)(?: Build|\\) AppleWebKit)', None, '$1', 'Toshiba', 'Folio 100'),
    DeviceMatcher('; {0,2}(AT[0-9]{2,3}(?:\\-A|LE\\-A|PE\\-A|SE|a|)|AT7-A|AT1S0|Hikari-iFrame/WDPF-[^;/]{1,100}|THRiVE|Thrive)(?: Build|\\) AppleWebKit)', None, 'Toshiba $1', 'Toshiba', '$1'),
    DeviceMatcher('; {0,2}(TM-MID\\d+[^;/]{1,50}|TOUCHMATE|MID-750)(?: Build|\\) AppleWebKit)', None, '$1', 'Touchmate', '$1'),
    DeviceMatcher('; {0,2}(TM-SM\\d+[^;/]{1,50}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Touchmate', '$1'),
    DeviceMatcher('; {0,2}(A10 [Bb]asic2?)(?: Build|\\) AppleWebKit)', None, '$1', 'Treq', '$1'),
    DeviceMatcher('; {0,2}(TREQ[ _\\-])([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', '$1$2', 'Treq', '$2'),
    DeviceMatcher('; {0,2}(X-?5|X-?3)(?: Build|\\) AppleWebKit)', None, '$1', 'Umeox', '$1'),
    DeviceMatcher('; {0,2}(A502\\+?|A936|A603|X1|X2)(?: Build|\\) AppleWebKit)', None, '$1', 'Umeox', '$1'),
    DeviceMatcher('; thor Build/', None, 'Thor', 'Vernee', 'Thor'),
    DeviceMatcher('; Thor (E)? Build/', None, 'Thor $1', 'Vernee', 'Thor'),
    DeviceMatcher('; Apollo Lite Build/', None, 'Apollo Lite', 'Vernee', 'Apollo'),
    DeviceMatcher('(TOUCH(?:TAB|PAD).{1,200}?)(?: Build|\\) AppleWebKit)', 'i', 'Versus $1', 'Versus', '$1'),
    DeviceMatcher('(VERTU) ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'Vertu', '$2'),
    DeviceMatcher('; {0,2}(Videocon)[ _\\-]([^;/]{1,100}?) {0,2}(?:Build|;)', None, '$1 $2', 'Videocon', '$2'),
    DeviceMatcher(' (VT\\d{2}[A-Za-z]*)(?: Build|\\) AppleWebKit)', None, '$1', 'Videocon', '$1'),
    DeviceMatcher('; {0,2}((?:ViewPad|ViewPhone|VSD)[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Viewsonic', '$1'),
    DeviceMatcher('; {0,2}(ViewSonic-)([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1$2', 'Viewsonic', '$2'),
    DeviceMatcher('; {0,2}(GTablet.{0,200}?)(?: Build|\\) AppleWebKit)', None, '$1', 'Viewsonic', '$1'),
    DeviceMatcher('; {0,2}([Vv]ivo)[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'vivo', '$2'),
    DeviceMatcher('(Vodafone) (.{0,200}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('; {0,2}(?:Walton[ _\\-]|)(Primo[ _\\-][^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', 'Walton $1', 'Walton', '$1'),
    DeviceMatcher('; {0,2}(?:WIKO[ \\-]|)(CINK\\+?|BARRY|BLOOM|DARKFULL|DARKMOON|DARKNIGHT|DARKSIDE|FIZZ|HIGHWAY|IGGY|OZZY|RAINBOW|STAIRWAY|SUBLIM|WAX|CINK [^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', 'Wiko $1', 'Wiko', '$1'),
    DeviceMatcher('; {0,2}WellcoM-([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Wellcom $1', 'Wellcom', '$1'),
    DeviceMatcher('(?:(WeTab)-Browser|; (wetab) Build)', None, '$1', 'WeTab', 'WeTab'),
    DeviceMatcher('; {0,2}(AT-AS[^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Wolfgang $1', 'Wolfgang', '$1'),
    DeviceMatcher('; {0,2}(?:Woxter|Wxt) ([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'Woxter $1', 'Woxter', '$1'),
    DeviceMatcher('; {0,2}(?:Xenta |Luna |)(TAB[234][0-9]{2}|TAB0[78]-\\d{3}|TAB0?9-\\d{3}|TAB1[03]-\\d{3}|SMP\\d{2}-\\d{3})(?: Build|\\) AppleWebKit)', None, 'Yarvik $1', 'Yarvik', '$1'),
    DeviceMatcher('; {0,2}([A-Z]{2,4})(M\\d{3,}[A-Z]{2})([^;\\)\\/]*)(?: Build|[;\\)])', None, 'Yifang $1$2$3', 'Yifang', '$2'),
    DeviceMatcher('; {0,2}((Mi|MI|HM|MI-ONE|Redmi)[ -](NOTE |Note |)[^;/]*) (Build|MIUI)/', None, 'XiaoMi $1', 'XiaoMi', '$1'),
    DeviceMatcher('; {0,2}((Mi|MI|HM|MI-ONE|Redmi)[ -](NOTE |Note |)[^;/\\)]*)', None, 'XiaoMi $1', 'XiaoMi', '$1'),
    DeviceMatcher('; {0,2}(MIX) (Build|MIUI)/', None, 'XiaoMi $1', 'XiaoMi', '$1'),
    DeviceMatcher('; {0,2}((MIX) ([^;/]*)) (Build|MIUI)/', None, 'XiaoMi $1', 'XiaoMi', '$1'),
    DeviceMatcher('; {0,2}XOLO[ _]([^;/]{0,30}tab.{0,30})(?: Build|\\) AppleWebKit)', 'i', 'Xolo $1', 'Xolo', '$1'),
    DeviceMatcher('; {0,2}XOLO[ _]([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', 'i', 'Xolo $1', 'Xolo', '$1'),
    DeviceMatcher('; {0,2}(q\\d0{2,3}[a-z]?)(?: Build|\\) AppleWebKit)', 'i', 'Xolo $1', 'Xolo', '$1'),
    DeviceMatcher('; {0,2}(PAD ?[79]\\d+[^;/]{0,50}|TelePAD\\d+[^;/])(?: Build|\\) AppleWebKit)', None, 'Xoro $1', 'Xoro', '$1'),
    DeviceMatcher('; {0,2}(?:(?:ZOPO|Zopo)[ _]([^;/]{1,100}?)|(ZP ?(?:\\d{2}[^;/]{1,100}|C2))|(C[2379]))(?: Build|\\) AppleWebKit)', None, '$1$2$3', 'Zopo', '$1$2$3'),
    DeviceMatcher('; {0,2}(ZiiLABS) (Zii[^;/]*)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'ZiiLabs', '$2'),
    DeviceMatcher('; {0,2}(Zii)_([^;/]*)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'ZiiLabs', '$2'),
    DeviceMatcher('; {0,2}(ARIZONA|(?:ATLAS|Atlas) W|D930|Grand (?:[SX][^;]{0,200}?|Era|Memo[^;]{0,200}?)|JOE|(?:Kis|KIS)\\b[^;]{0,200}?|Libra|Light [^;]{0,200}?|N8[056][01]|N850L|N8000|N9[15]\\d{2}|N9810|NX501|Optik|(?:Vip )Racer[^;]{0,200}?|RacerII|RACERII|San Francisco[^;]{0,200}?|V9[AC]|V55|V881|Z[679][0-9]{2}[A-z]?)(?: Build|\\) AppleWebKit)', None, '$1', 'ZTE', '$1'),
    DeviceMatcher('; {0,2}([A-Z]\\d+)_USA_[^;]{0,200}(?: Build|\\) AppleWebKit)', None, '$1', 'ZTE', '$1'),
    DeviceMatcher('; {0,2}(SmartTab\\d+)[^;]{0,50}(?: Build|\\) AppleWebKit)', None, '$1', 'ZTE', '$1'),
    DeviceMatcher('; {0,2}(?:Blade|BLADE|ZTE-BLADE)([^;/]*)(?: Build|\\) AppleWebKit)', None, 'ZTE Blade$1', 'ZTE', 'Blade$1'),
    DeviceMatcher('; {0,2}(?:Skate|SKATE|ZTE-SKATE)([^;/]*)(?: Build|\\) AppleWebKit)', None, 'ZTE Skate$1', 'ZTE', 'Skate$1'),
    DeviceMatcher('; {0,2}(Orange |Optimus )(Monte Carlo|San Francisco)(?: Build|\\) AppleWebKit)', None, '$1$2', 'ZTE', '$1$2'),
    DeviceMatcher('; {0,2}(?:ZXY-ZTE_|ZTE\\-U |ZTE[\\- _]|ZTE-C[_ ])([^;/]{1,100}?)(?: Build|\\) AppleWebKit)', None, 'ZTE $1', 'ZTE', '$1'),
    DeviceMatcher('; (BASE) (lutea|Lutea 2|Tab[^;]{0,200}?)(?: Build|\\) AppleWebKit)', None, '$1 $2', 'ZTE', '$1 $2'),
    DeviceMatcher('; (Avea inTouch 2|soft stone|tmn smart a7|Movistar[ _]Link)(?: Build|\\) AppleWebKit)', 'i', '$1', 'ZTE', '$1'),
    DeviceMatcher('; {0,2}(vp9plus)\\)', None, '$1', 'ZTE', '$1'),
    DeviceMatcher('; ?(Cloud[ _]Z5|z1000|Z99 2G|z99|z930|z999|z990|z909|Z919|z900)(?: Build|\\) AppleWebKit)', None, '$1', 'Zync', '$1'),
    DeviceMatcher('; ?(KFOT|Kindle Fire) Build\\b', None, 'Kindle Fire', 'Amazon', 'Kindle Fire'),
    DeviceMatcher('; ?(KFOTE|Amazon Kindle Fire2) Build\\b', None, 'Kindle Fire 2', 'Amazon', 'Kindle Fire 2'),
    DeviceMatcher('; ?(KFTT) Build\\b', None, 'Kindle Fire HD', 'Amazon', 'Kindle Fire HD 7"'),
    DeviceMatcher('; ?(KFJWI) Build\\b', None, 'Kindle Fire HD 8.9" WiFi', 'Amazon', 'Kindle Fire HD 8.9" WiFi'),
    DeviceMatcher('; ?(KFJWA) Build\\b', None, 'Kindle Fire HD 8.9" 4G', 'Amazon', 'Kindle Fire HD 8.9" 4G'),
    DeviceMatcher('; ?(KFSOWI) Build\\b', None, 'Kindle Fire HD 7" WiFi', 'Amazon', 'Kindle Fire HD 7" WiFi'),
    DeviceMatcher('; ?(KFTHWI) Build\\b', None, 'Kindle Fire HDX 7" WiFi', 'Amazon', 'Kindle Fire HDX 7" WiFi'),
    DeviceMatcher('; ?(KFTHWA) Build\\b', None, 'Kindle Fire HDX 7" 4G', 'Amazon', 'Kindle Fire HDX 7" 4G'),
    DeviceMatcher('; ?(KFAPWI) Build\\b', None, 'Kindle Fire HDX 8.9" WiFi', 'Amazon', 'Kindle Fire HDX 8.9" WiFi'),
    DeviceMatcher('; ?(KFAPWA) Build\\b', None, 'Kindle Fire HDX 8.9" 4G', 'Amazon', 'Kindle Fire HDX 8.9" 4G'),
    DeviceMatcher('; ?Amazon ([^;/]{1,100}) Build\\b', None, '$1', 'Amazon', '$1'),
    DeviceMatcher('; ?(Kindle) Build\\b', None, 'Kindle', 'Amazon', 'Kindle'),
    DeviceMatcher('; ?(Silk)/(\\d+)\\.(\\d+)(?:\\.([0-9\\-]+)|) Build\\b', None, 'Kindle Fire', 'Amazon', 'Kindle Fire$2'),
    DeviceMatcher(' (Kindle)/(\\d+\\.\\d+)', None, 'Kindle', 'Amazon', '$1 $2'),
    DeviceMatcher(' (Silk|Kindle)/(\\d+)\\.', None, 'Kindle', 'Amazon', 'Kindle'),
    DeviceMatcher('(sprd)\\-([^/]{1,50})/', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('; {0,2}(H\\d{2}00\\+?) Build', None, '$1', 'Hero', '$1'),
    DeviceMatcher('; {0,2}(iphone|iPhone5) Build/', None, 'Xianghe $1', 'Xianghe', '$1'),
    DeviceMatcher('; {0,2}(e\\d{4}[a-z]?_?v\\d+|v89_[^;/]{1,100})[^;/]{1,30} Build/', None, 'Xianghe $1', 'Xianghe', '$1'),
    DeviceMatcher('\\bUSCC[_\\-]?([^ ;/\\)]+)', None, '$1', 'Cellular', '$1'),
    DeviceMatcher('Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:ALCATEL)[^;]{0,200}; {0,2}([^;,\\)]+)', None, 'Alcatel $1', 'Alcatel', '$1'),
    DeviceMatcher('Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|WpsLondonTest; ?|)(?:ASUS|Asus)[^;]{0,200}; {0,2}([^;,\\)]+)', None, 'Asus $1', 'Asus', '$1'),
    DeviceMatcher('Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:DELL|Dell)[^;]{0,200}; {0,2}([^;,\\)]+)', None, 'Dell $1', 'Dell', '$1'),
    DeviceMatcher('Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|WpsLondonTest; ?|)(?:HTC|Htc|HTC_blocked[^;]{0,200})[^;]{0,200}; {0,2}(?:HTC|)([^;,\\)]+)', None, 'HTC $1', 'HTC', '$1'),
    DeviceMatcher('Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:HUAWEI)[^;]{0,200}; {0,2}(?:HUAWEI |)([^;,\\)]+)', None, 'Huawei $1', 'Huawei', '$1'),
    DeviceMatcher('Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:LG|Lg)[^;]{0,200}; {0,2}(?:LG[ \\-]|)([^;,\\)]+)', None, 'LG $1', 'LG', '$1'),
    DeviceMatcher('Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:rv:11; |)(?:NOKIA|Nokia)[^;]{0,200}; {0,2}(?:NOKIA ?|Nokia ?|LUMIA ?|[Ll]umia ?|)(\\d{3,10}[^;\\)]*)', None, 'Lumia $1', 'Nokia', 'Lumia $1'),
    DeviceMatcher('Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:NOKIA|Nokia)[^;]{0,200}; {0,2}(RM-\\d{3,})', None, 'Nokia $1', 'Nokia', '$1'),
    DeviceMatcher('(?:Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)]|WPDesktop;) ?(?:ARM; ?Touch; ?|Touch; ?|)(?:NOKIA|Nokia)[^;]{0,200}; {0,2}(?:NOKIA ?|Nokia ?|LUMIA ?|[Ll]umia ?|)([^;\\)]+)', None, 'Nokia $1', 'Nokia', '$1'),
    DeviceMatcher('Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:Microsoft(?: Corporation|))[^;]{0,200}; {0,2}([^;,\\)]+)', None, 'Microsoft $1', 'Microsoft', '$1'),
    DeviceMatcher('Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|WpsLondonTest; ?|)(?:SAMSUNG)[^;]{0,200}; {0,2}(?:SAMSUNG |)([^;,\\.\\)]+)', None, 'Samsung $1', 'Samsung', '$1'),
    DeviceMatcher('Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|WpsLondonTest; ?|)(?:TOSHIBA|FujitsuToshibaMobileCommun)[^;]{0,200}; {0,2}([^;,\\)]+)', None, 'Toshiba $1', 'Toshiba', '$1'),
    DeviceMatcher('Windows Phone [^;]{1,30}; .{0,100}?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|WpsLondonTest; ?|)([^;]{1,200}); {0,2}([^;,\\)]+)', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('(?:^|; )SAMSUNG\\-([A-Za-z0-9\\-]{1,50}).{0,200} Bada/', None, 'Samsung $1', 'Samsung', '$1'),
    DeviceMatcher('\\(Mobile; ALCATEL ?(One|ONE) ?(Touch|TOUCH) ?([^;/]{1,100}?)(?:/[^;]{1,200}|); rv:[^\\)]{1,200}\\) Gecko/[^\\/]{1,200} Firefox/', None, 'Alcatel $1 $2 $3', 'Alcatel', 'One Touch $3'),
    DeviceMatcher('\\(Mobile; (?:ZTE([^;]{1,200})|(OpenC)); rv:[^\\)]{1,200}\\) Gecko/[^\\/]{1,200} Firefox/', None, 'ZTE $1$2', 'ZTE', '$1$2'),
    DeviceMatcher('\\(Mobile; ALCATEL([A-Za-z0-9\\-]+); rv:[^\\)]{1,200}\\) Gecko/[^\\/]{1,200} Firefox/[^\\/]{1,200} KaiOS/', None, 'Alcatel $1', 'Alcatel', '$1'),
    DeviceMatcher('\\(Mobile; LYF\\/([A-Za-z0-9\\-]{1,100})\\/.{0,100};.{0,100}rv:[^\\)]{1,100}\\) Gecko/[^\\/]{1,100} Firefox/[^\\/]{1,100} KAIOS/', None, 'LYF $1', 'LYF', '$1'),
    DeviceMatcher('\\(Mobile; Nokia_([A-Za-z0-9\\-]{1,100})_.{1,100}; rv:[^\\)]{1,100}\\) Gecko/[^\\/]{1,100} Firefox/[^\\/]{1,100} KAIOS/', None, 'Nokia $1', 'Nokia', '$1'),
    DeviceMatcher('Nokia(N[0-9]+)([A-Za-z_\\-][A-Za-z0-9_\\-]*)', None, 'Nokia $1', 'Nokia', '$1$2'),
    DeviceMatcher('(?:NOKIA|Nokia)(?:\\-| {0,2})(?:([A-Za-z0-9]+)\\-[0-9a-f]{32}|([A-Za-z0-9\\-]+)(?:UCBrowser)|([A-Za-z0-9\\-]+))', None, 'Nokia $1$2$3', 'Nokia', '$1$2$3'),
    DeviceMatcher('Lumia ([A-Za-z0-9\\-]+)', None, 'Lumia $1', 'Nokia', 'Lumia $1'),
    DeviceMatcher('\\(Symbian; U; S60 V5; [A-z]{2}\\-[A-z]{2}; (SonyEricsson|Samsung|Nokia|LG)([^;/]{1,100}?)\\)', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('\\(Symbian(?:/3|); U; ([^;]{1,200});', None, 'Nokia $1', 'Nokia', '$1'),
    DeviceMatcher('BB10; ([A-Za-z0-9\\- ]+)\\)', None, 'BlackBerry $1', 'BlackBerry', '$1'),
    DeviceMatcher('Play[Bb]ook.{1,200}RIM Tablet OS', None, 'BlackBerry Playbook', 'BlackBerry', 'Playbook'),
    DeviceMatcher('Black[Bb]erry ([0-9]+);', None, 'BlackBerry $1', 'BlackBerry', '$1'),
    DeviceMatcher('Black[Bb]erry([0-9]+)', None, 'BlackBerry $1', 'BlackBerry', '$1'),
    DeviceMatcher('Black[Bb]erry;', None, 'BlackBerry', 'BlackBerry'),
    DeviceMatcher('(Pre|Pixi)/\\d+\\.\\d+', None, 'Palm $1', 'Palm', '$1'),
    DeviceMatcher('Palm([0-9]+)', None, 'Palm $1', 'Palm', '$1'),
    DeviceMatcher('Treo([A-Za-z0-9]+)', None, 'Palm Treo $1', 'Palm', 'Treo $1'),
    DeviceMatcher('webOS.{0,200}(P160U(?:NA|))/(\\d+).(\\d+)', None, 'HP Veer', 'HP', 'Veer'),
    DeviceMatcher('(Touch[Pp]ad)/\\d+\\.\\d+', None, 'HP TouchPad', 'HP', 'TouchPad'),
    DeviceMatcher('HPiPAQ([A-Za-z0-9]{1,20})/\\d+\\.\\d+', None, 'HP iPAQ $1', 'HP', 'iPAQ $1'),
    DeviceMatcher('PDA; (PalmOS)/sony/model ([a-z]+)/Revision', None, '$1', 'Sony', '$1 $2'),
    DeviceMatcher('(Apple\\s?TV)', None, 'AppleTV', 'Apple', 'AppleTV'),
    DeviceMatcher('(QtCarBrowser)', None, 'Tesla Model S', 'Tesla', 'Model S'),
    DeviceMatcher('(iPhone|iPad|iPod)(\\d+,\\d+)', None, '$1', 'Apple', '$1$2'),
    DeviceMatcher('(iPad)(?:;| Simulator;)', None, '$1', 'Apple', '$1'),
    DeviceMatcher('(iPod)(?:;| touch;| Simulator;)', None, '$1', 'Apple', '$1'),
    DeviceMatcher('(iPhone)(?:;| Simulator;)', None, '$1', 'Apple', '$1'),
    DeviceMatcher('(Watch)(\\d+,\\d+)', None, 'Apple $1', 'Apple', '$1$2'),
    DeviceMatcher('(Apple Watch)(?:;| Simulator;)', None, '$1', 'Apple', '$1'),
    DeviceMatcher('(HomePod)(?:;| Simulator;)', None, '$1', 'Apple', '$1'),
    DeviceMatcher('iPhone', None, 'iPhone', 'Apple', 'iPhone'),
    DeviceMatcher('CFNetwork/.{0,100} Darwin/\\d.{0,100}\\(((?:Mac|iMac|PowerMac|PowerBook)[^\\d]*)(\\d+)(?:,|%2C)(\\d+)', None, '$1$2,$3', 'Apple', '$1$2,$3'),
    DeviceMatcher('CFNetwork/.{0,100} Darwin/\\d+\\.\\d+\\.\\d+ \\(x86_64\\)', None, 'Mac', 'Apple', 'Mac'),
    DeviceMatcher('CFNetwork/.{0,100} Darwin/\\d', None, 'iOS-Device', 'Apple', 'iOS-Device'),
    DeviceMatcher('Outlook-(iOS)/\\d+\\.\\d+\\.prod\\.iphone', None, 'iPhone', 'Apple', 'iPhone'),
    DeviceMatcher('acer_([A-Za-z0-9]+)_', None, 'Acer $1', 'Acer', '$1'),
    DeviceMatcher('(?:ALCATEL|Alcatel)-([A-Za-z0-9\\-]+)', None, 'Alcatel $1', 'Alcatel', '$1'),
    DeviceMatcher('(?:Amoi|AMOI)\\-([A-Za-z0-9]+)', None, 'Amoi $1', 'Amoi', '$1'),
    DeviceMatcher('(?:; |\\/|^)((?:Transformer (?:Pad|Prime) |Transformer |PadFone[ _]?)[A-Za-z0-9]*)', None, 'Asus $1', 'Asus', '$1'),
    DeviceMatcher('(?:asus.{0,200}?ASUS|Asus|ASUS|asus)[\\- ;]*((?:Transformer (?:Pad|Prime) |Transformer |Padfone |Nexus[ _]|)[A-Za-z0-9]+)', None, 'Asus $1', 'Asus', '$1'),
    DeviceMatcher('(?:ASUS)_([A-Za-z0-9\\-]+)', None, 'Asus $1', 'Asus', '$1'),
    DeviceMatcher('\\bBIRD[ \\-\\.]([A-Za-z0-9]+)', None, 'Bird $1', 'Bird', '$1'),
    DeviceMatcher('\\bDell ([A-Za-z0-9]+)', None, 'Dell $1', 'Dell', '$1'),
    DeviceMatcher('DoCoMo/2\\.0 ([A-Za-z0-9]+)', None, 'DoCoMo $1', 'DoCoMo', '$1'),
    DeviceMatcher('^.{0,50}?([A-Za-z0-9]{1,30})_W;FOMA', None, 'DoCoMo $1', 'DoCoMo', '$1'),
    DeviceMatcher('^.{0,50}?([A-Za-z0-9]{1,30});FOMA', None, 'DoCoMo $1', 'DoCoMo', '$1'),
    DeviceMatcher('\\b(?:HTC/|HTC/[a-z0-9]{1,20}/|)HTC[ _\\-;]? {0,2}(.{0,200}?)(?:-?Mozilla|fingerPrint|[;/\\(\\)]|$)', None, 'HTC $1', 'HTC', '$1'),
    DeviceMatcher('Huawei([A-Za-z0-9]+)', None, 'Huawei $1', 'Huawei', '$1'),
    DeviceMatcher('HUAWEI-([A-Za-z0-9]+)', None, 'Huawei $1', 'Huawei', '$1'),
    DeviceMatcher('HUAWEI ([A-Za-z0-9\\-]+)', None, 'Huawei $1', 'Huawei', '$1'),
    DeviceMatcher('vodafone([A-Za-z0-9]+)', None, 'Huawei Vodafone $1', 'Huawei', 'Vodafone $1'),
    DeviceMatcher('i\\-mate ([A-Za-z0-9]+)', None, 'i-mate $1', 'i-mate', '$1'),
    DeviceMatcher('Kyocera\\-([A-Za-z0-9]+)', None, 'Kyocera $1', 'Kyocera', '$1'),
    DeviceMatcher('KWC\\-([A-Za-z0-9]+)', None, 'Kyocera $1', 'Kyocera', '$1'),
    DeviceMatcher('Lenovo[_\\-]([A-Za-z0-9]+)', None, 'Lenovo $1', 'Lenovo', '$1'),
    DeviceMatcher('(HbbTV)/[0-9]+\\.[0-9]+\\.[0-9]+ \\( ?;(LG)E ?;([^;]{0,30})', None, '$1', '$2', '$3'),
    DeviceMatcher('(HbbTV)/1\\.1\\.1.{0,200}CE-HTML/1\\.\\d;(Vendor/|)(THOM[^;]{0,200}?)[;\\s].{0,30}(LF[^;]{1,200});?', None, '$1', 'Thomson', '$4'),
    DeviceMatcher('(HbbTV)(?:/1\\.1\\.1|) ?(?: \\(;;;;;\\)|); {0,2}CE-HTML(?:/1\\.\\d|); {0,2}([^ ]{1,30}) ([^;]{1,200});', None, '$1', '$2', '$3'),
    DeviceMatcher('(HbbTV)/1\\.1\\.1 \\(;;;;;\\) Maple_2011', None, '$1', 'Samsung'),
    DeviceMatcher('(HbbTV)/[0-9]+\\.[0-9]+\\.[0-9]+ \\([^;]{0,30}; ?(?:CUS:([^;]{0,200})|([^;]{1,200})) ?; ?([^;]{0,30})', None, '$1', '$2$3', '$4'),
    DeviceMatcher('(HbbTV)/[0-9]+\\.[0-9]+\\.[0-9]+', None, '$1'),
    DeviceMatcher('LGE; (?:Media\\/|)([^;]{0,200});[^;]{0,200};[^;]{0,200};?\\); "?LG NetCast(\\.TV|\\.Media|)-\\d+', None, 'NetCast$2', 'LG', '$1'),
    DeviceMatcher('InettvBrowser/[0-9]{1,30}\\.[0-9A-Z]{1,30} \\([^;]{0,200};(Sony)([^;]{0,200});[^;]{0,200};[^\\)]{0,10}\\)', None, 'Inettv', '$1', '$2'),
    DeviceMatcher('InettvBrowser/[0-9]{1,30}\\.[0-9A-Z]{1,30} \\([^;]{0,200};([^;]{0,200});[^;]{0,200};[^\\)]{0,10}\\)', None, 'Inettv', 'Generic_Inettv', '$1'),
    DeviceMatcher('(?:InettvBrowser|TSBNetTV|NETTV|HBBTV)', None, 'Inettv', 'Generic_Inettv'),
    DeviceMatcher('Series60/\\d\\.\\d (LG)[\\-]?([A-Za-z0-9 \\-]+)', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('\\b(?:LGE[ \\-]LG\\-(?:AX|)|LGE |LGE?-LG|LGE?[ \\-]|LG[ /\\-]|lg[\\-])([A-Za-z0-9]+)\\b', None, 'LG $1', 'LG', '$1'),
    DeviceMatcher('(?:^LG[\\-]?|^LGE[\\-/]?)([A-Za-z]+[0-9]+[A-Za-z]*)', None, 'LG $1', 'LG', '$1'),
    DeviceMatcher('^LG([0-9]+[A-Za-z]*)', None, 'LG $1', 'LG', '$1'),
    DeviceMatcher('(KIN\\.[^ ]+) (\\d+)\\.(\\d+)', None, 'Microsoft $1', 'Microsoft', '$1'),
    DeviceMatcher('(?:MSIE|XBMC).{0,200}\\b(Xbox)\\b', None, '$1', 'Microsoft', '$1'),
    DeviceMatcher('; ARM; Trident/6\\.0; Touch[\\);]', None, 'Microsoft Surface RT', 'Microsoft', 'Surface RT'),
    DeviceMatcher('Motorola\\-([A-Za-z0-9]+)', None, 'Motorola $1', 'Motorola', '$1'),
    DeviceMatcher('MOTO\\-([A-Za-z0-9]+)', None, 'Motorola $1', 'Motorola', '$1'),
    DeviceMatcher('MOT\\-([A-z0-9][A-z0-9\\-]*)', None, 'Motorola $1', 'Motorola', '$1'),
    DeviceMatcher('; (moto[ a-zA-z0-9()]{0,50});((?: Build|.{0,50}\\) AppleWebKit))', None, '$1', 'Motorola', '$1'),
    DeviceMatcher('; {0,2}(moto)(.{0,50})(?: Build|\\) AppleWebKit)', None, 'Motorola$2', 'Motorola', '$2'),
    DeviceMatcher('Nintendo WiiU', None, 'Nintendo Wii U', 'Nintendo', 'Wii U'),
    DeviceMatcher('Nintendo (DS|3DS|DSi|Wii);', None, 'Nintendo $1', 'Nintendo', '$1'),
    DeviceMatcher('(?:Pantech|PANTECH)[ _-]?([A-Za-z0-9\\-]+)', None, 'Pantech $1', 'Pantech', '$1'),
    DeviceMatcher('Philips([A-Za-z0-9]+)', None, 'Philips $1', 'Philips', '$1'),
    DeviceMatcher('Philips ([A-Za-z0-9]+)', None, 'Philips $1', 'Philips', '$1'),
    DeviceMatcher('(SMART-TV); .{0,200} Tizen ', None, 'Samsung $1', 'Samsung', '$1'),
    DeviceMatcher('SymbianOS/9\\.\\d.{0,200} Samsung[/\\-]([A-Za-z0-9 \\-]+)', None, 'Samsung $1', 'Samsung', '$1'),
    DeviceMatcher('(Samsung)(SGH)(i[0-9]+)', None, '$1 $2$3', '$1', '$2-$3'),
    DeviceMatcher('SAMSUNG-ANDROID-MMS/([^;/]{1,100})', None, '$1', 'Samsung', '$1'),
    DeviceMatcher('SAMSUNG(?:; |[ -/])([A-Za-z0-9\\-]+)', 'i', 'Samsung $1', 'Samsung', '$1'),
    DeviceMatcher('(Dreamcast)', None, 'Sega $1', 'Sega', '$1'),
    DeviceMatcher('^SIE-([A-Za-z0-9]+)', None, 'Siemens $1', 'Siemens', '$1'),
    DeviceMatcher('Softbank/[12]\\.0/([A-Za-z0-9]+)', None, 'Softbank $1', 'Softbank', '$1'),
    DeviceMatcher('SonyEricsson ?([A-Za-z0-9\\-]+)', None, 'Ericsson $1', 'SonyEricsson', '$1'),
    DeviceMatcher('Android [^;]{1,200}; ([^ ]+) (Sony)/', None, '$2 $1', '$2', '$1'),
    DeviceMatcher('(Sony)(?:BDP\\/|\\/|)([^ /;\\)]+)[ /;\\)]', None, '$1 $2', '$1', '$2'),
    DeviceMatcher('Puffin/[\\d\\.]+IT', None, 'iPad', 'Apple', 'iPad'),
    DeviceMatcher('Puffin/[\\d\\.]+IP', None, 'iPhone', 'Apple', 'iPhone'),
    DeviceMatcher('Puffin/[\\d\\.]+AT', None, 'Generic Tablet', 'Generic', 'Tablet'),
    DeviceMatcher('Puffin/[\\d\\.]+AP', None, 'Generic Smartphone', 'Generic', 'Smartphone'),
    DeviceMatcher('Android[\\- ][\\d]+\\.[\\d]+; [A-Za-z]{2}\\-[A-Za-z]{0,2}; WOWMobile (.{1,200})( Build[/ ]|\\))', None, None, 'Generic_Android', '$1'),
    DeviceMatcher('Android[\\- ][\\d]+\\.[\\d]+\\-update1; [A-Za-z]{2}\\-[A-Za-z]{0,2} {0,2}; {0,2}(.{1,200}?)( Build[/ ]|\\))', None, None, 'Generic_Android', '$1'),
    DeviceMatcher('Android[\\- ][\\d]+(?:\\.[\\d]+)(?:\\.[\\d]+|); {0,2}[A-Za-z]{2}[_\\-][A-Za-z]{0,2}\\-? {0,2}; {0,2}(.{1,200}?)( Build[/ ]|\\))', None, None, 'Generic_Android', '$1'),
    DeviceMatcher('Android[\\- ][\\d]+(?:\\.[\\d]+)(?:\\.[\\d]+|); {0,2}[A-Za-z]{0,2}\\- {0,2}; {0,2}(.{1,200}?)( Build[/ ]|\\))', None, None, 'Generic_Android', '$1'),
    DeviceMatcher('Android[\\- ][\\d]+(?:\\.[\\d]+)(?:\\.[\\d]+|); {0,2}[a-z]{0,2}[_\\-]?[A-Za-z]{0,2};?( Build[/ ]|\\))', None, 'Generic Smartphone', 'Generic', 'Smartphone'),
    DeviceMatcher('Android[\\- ][\\d]+(?:\\.[\\d]+)(?:\\.[\\d]+|); {0,3}\\-?[A-Za-z]{2}; {0,2}(.{1,50}?)( Build[/ ]|\\))', None, None, 'Generic_Android', '$1'),
    DeviceMatcher('Android \\d+?(?:\\.\\d+|)(?:\\.\\d+|); ([^;]{1,100}?)(?: Build|\\) AppleWebKit).{1,200}? Mobile Safari', None, None, 'Generic_Android', '$1'),
    DeviceMatcher('Android \\d+?(?:\\.\\d+|)(?:\\.\\d+|); ([^;]{1,100}?)(?: Build|\\) AppleWebKit).{1,200}? Safari', None, None, 'Generic_Android_Tablet', '$1'),
    DeviceMatcher('Android \\d+?(?:\\.\\d+|)(?:\\.\\d+|); ([^;]{1,100}?)(?: Build|\\))', None, None, 'Generic_Android', '$1'),
    DeviceMatcher('(GoogleTV)', None, None, 'Generic_Inettv', '$1'),
    DeviceMatcher('(WebTV)/\\d+.\\d+', None, None, 'Generic_Inettv', '$1'),
    DeviceMatcher('^(Roku)/DVP-\\d+\\.\\d+', None, None, 'Generic_Inettv', '$1'),
    DeviceMatcher('(Android 3\\.\\d|Opera Tablet|Tablet; .{1,100}Firefox/|Android.{0,100}(?:Tab|Pad))', 'i', 'Generic Tablet', 'Generic', 'Tablet'),
    DeviceMatcher('(Symbian|\\bS60(Version|V\\d)|\\bS60\\b|\\((Series 60|Windows Mobile|Palm OS|Bada); Opera Mini|Windows CE|Opera Mobi|BREW|Brew|Mobile; .{1,200}Firefox/|iPhone OS|Android|MobileSafari|Windows {0,2}Phone|\\(webOS/|PalmOS)', None, 'Generic Smartphone', 'Generic', 'Smartphone'),
    DeviceMatcher('(hiptop|avantgo|plucker|xiino|blazer|elaine)', 'i', 'Generic Smartphone', 'Generic', 'Smartphone'),
    DeviceMatcher('^.{0,100}(bot|BUbiNG|zao|borg|DBot|oegp|silk|Xenu|zeal|^NING|CCBot|crawl|htdig|lycos|slurp|teoma|voila|yahoo|Sogou|CiBra|Nutch|^Java/|^JNLP/|Daumoa|Daum|Genieo|ichiro|larbin|pompos|Scrapy|snappy|speedy|spider|msnbot|msrbot|vortex|^vortex|crawler|favicon|indexer|Riddler|scooter|scraper|scrubby|WhatWeb|WinHTTP|bingbot|BingPreview|openbot|gigabot|furlbot|polybot|seekbot|^voyager|archiver|Icarus6j|mogimogi|Netvibes|blitzbot|altavista|charlotte|findlinks|Retreiver|TLSProber|WordPress|SeznamBot|ProoXiBot|wsr\\-agent|Squrl Java|EtaoSpider|PaperLiBot|SputnikBot|A6\\-Indexer|netresearch|searchsight|baiduspider|YisouSpider|ICC\\-Crawler|http%20client|Python-urllib|dataparksearch|converacrawler|Screaming Frog|AppEngine-Google|YahooCacheSystem|fast\\-webcrawler|Sogou Pic Spider|semanticdiscovery|Innovazion Crawler|facebookexternalhit|Google.{0,200}/\\+/web/snippet|Google-HTTP-Java-Client|BlogBridge|IlTrovatore-Setaccio|InternetArchive|GomezAgent|WebThumbnail|heritrix|NewsGator|PagePeeker|Reaper|ZooShot|holmes|NL-Crawler|Pingdom|StatusCake|WhatsApp|masscan|Google Web Preview|Qwantify|Yeti|OgScrper)', 'i', 'Spider', 'Spider', 'Desktop'),
    DeviceMatcher('^(1207|3gso|4thp|501i|502i|503i|504i|505i|506i|6310|6590|770s|802s|a wa|acer|acs\\-|airn|alav|asus|attw|au\\-m|aur |aus |abac|acoo|aiko|alco|alca|amoi|anex|anny|anyw|aptu|arch|argo|bmobile|bell|bird|bw\\-n|bw\\-u|beck|benq|bilb|blac|c55/|cdm\\-|chtm|capi|comp|cond|dall|dbte|dc\\-s|dica|ds\\-d|ds12|dait|devi|dmob|doco|dopo|dorado|el(?:38|39|48|49|50|55|58|68)|el[3456]\\d{2}dual|erk0|esl8|ex300|ez40|ez60|ez70|ezos|ezze|elai|emul|eric|ezwa|fake|fly\\-|fly_|g\\-mo|g1 u|g560|gf\\-5|grun|gene|go.w|good|grad|hcit|hd\\-m|hd\\-p|hd\\-t|hei\\-|hp i|hpip|hs\\-c|htc |htc\\-|htca|htcg)', 'i', 'Generic Feature Phone', 'Generic', 'Feature Phone'),
    DeviceMatcher('^(htcp|htcs|htct|htc_|haie|hita|huaw|hutc|i\\-20|i\\-go|i\\-ma|i\\-mobile|i230|iac|iac\\-|iac/|ig01|im1k|inno|iris|jata|kddi|kgt|kgt/|kpt |kwc\\-|klon|lexi|lg g|lg\\-a|lg\\-b|lg\\-c|lg\\-d|lg\\-f|lg\\-g|lg\\-k|lg\\-l|lg\\-m|lg\\-o|lg\\-p|lg\\-s|lg\\-t|lg\\-u|lg\\-w|lg/k|lg/l|lg/u|lg50|lg54|lge\\-|lge/|leno|m1\\-w|m3ga|m50/|maui|mc01|mc21|mcca|medi|meri|mio8|mioa|mo01|mo02|mode|modo|mot |mot\\-|mt50|mtp1|mtv |mate|maxo|merc|mits|mobi|motv|mozz|n100|n101|n102|n202|n203|n300|n302|n500|n502|n505|n700|n701|n710|nec\\-|nem\\-|newg|neon)', 'i', 'Generic Feature Phone', 'Generic', 'Feature Phone'),
    DeviceMatcher('^(netf|noki|nzph|o2 x|o2\\-x|opwv|owg1|opti|oran|ot\\-s|p800|pand|pg\\-1|pg\\-2|pg\\-3|pg\\-6|pg\\-8|pg\\-c|pg13|phil|pn\\-2|pt\\-g|palm|pana|pire|pock|pose|psio|qa\\-a|qc\\-2|qc\\-3|qc\\-5|qc\\-7|qc07|qc12|qc21|qc32|qc60|qci\\-|qwap|qtek|r380|r600|raks|rim9|rove|s55/|sage|sams|sc01|sch\\-|scp\\-|sdk/|se47|sec\\-|sec0|sec1|semc|sgh\\-|shar|sie\\-|sk\\-0|sl45|slid|smb3|smt5|sp01|sph\\-|spv |spv\\-|sy01|samm|sany|sava|scoo|send|siem|smar|smit|soft|sony|t\\-mo|t218|t250|t600|t610|t618|tcl\\-|tdg\\-|telm|tim\\-|ts70|tsm\\-|tsm3|tsm5|tx\\-9|tagt)', 'i', 'Generic Feature Phone', 'Generic', 'Feature Phone'),
    DeviceMatcher('^(talk|teli|topl|tosh|up.b|upg1|utst|v400|v750|veri|vk\\-v|vk40|vk50|vk52|vk53|vm40|vx98|virg|vertu|vite|voda|vulc|w3c |w3c\\-|wapj|wapp|wapu|wapm|wig |wapi|wapr|wapv|wapy|wapa|waps|wapt|winc|winw|wonu|x700|xda2|xdag|yas\\-|your|zte\\-|zeto|aste|audi|avan|blaz|brew|brvw|bumb|ccwa|cell|cldc|cmd\\-|dang|eml2|fetc|hipt|http|ibro|idea|ikom|ipaq|jbro|jemu|jigs|keji|kyoc|kyok|libw|m\\-cr|midp|mmef|moto|mwbp|mywa|newt|nok6|o2im|pant|pdxg|play|pluc|port|prox|rozo|sama|seri|smal|symb|treo|upsi|vx52|vx53|vx60|vx61|vx70|vx80|vx81|vx83|vx85|wap\\-|webc|whit|wmlb|xda\\-|xda_)', 'i', 'Generic Feature Phone', 'Generic', 'Feature Phone'),
    DeviceMatcher('^(Ice)$', None, 'Generic Feature Phone', 'Generic', 'Feature Phone'),
    DeviceMatcher('(wap[\\-\\ ]browser|maui|netfront|obigo|teleca|up\\.browser|midp|Opera Mini)', 'i', 'Generic Feature Phone', 'Generic', 'Feature Phone'),
    DeviceMatcher('Mac OS', None, 'Mac', 'Apple', 'Mac'),
])
