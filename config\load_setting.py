import yaml
from pydantic import BaseModel, Field

class ModelApiConfig(BaseModel):
    api_key: str = Field(..., description="DEEPSEEK_API_KEY")
    base_url: str = Field(..., description="DEEPSEEK_BASE_URL")

class WebCrawlerConfig(BaseModel):
    max_depth: int = Field(..., description="MAX_DEPTH")
    max_links: int = Field(..., description="MAX_LINKS")


class Config(BaseModel):
    model_api_config: ModelApiConfig
    web_crawler_config: WebCrawlerConfig

def load_setting():
    with open("config/settings.yaml", "r") as f:
        settings = yaml.safe_load(f)
        return Config(**settings)
    
config = load_setting()