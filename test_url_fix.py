"""
测试URL修复功能
"""

def normalize_url(url):
    """
    规范化URL，确保包含协议
    """
    if not url.startswith(('http://', 'https://')):
        return f"https://{url}"
    return url

# 测试URL规范化
test_urls = [
    "www.sling-stones.com",
    "example.com",
    "https://www.google.com",
    "http://httpbin.org",
    "github.com/user/repo"
]

print("URL规范化测试:")
print("=" * 40)

for url in test_urls:
    normalized = normalize_url(url)
    print(f"原始: {url}")
    print(f"规范: {normalized}")
    print("-" * 20)

# 测试爬虫URL解析
from urllib.parse import urlparse

print("\nURL解析测试:")
print("=" * 40)

for url in ["www.sling-stones.com", "https://www.sling-stones.com"]:
    parsed = urlparse(url)
    print(f"URL: {url}")
    print(f"  协议: {parsed.scheme}")
    print(f"  域名: {parsed.netloc}")
    print(f"  路径: {parsed.path}")
    print("-" * 20)
