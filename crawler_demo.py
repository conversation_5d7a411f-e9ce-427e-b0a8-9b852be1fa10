"""
网页爬虫功能演示
展示 SafeWebCrawler 的主要功能
"""

from web_crawler import SafeWebCrawler
from config import config

def demo_crawler():
    print("🕷️  网页爬虫功能演示")
    print("=" * 50)
    
    # 创建爬虫实例
    crawler = SafeWebCrawler(config)
    
    print(f"📋 爬虫配置:")
    print(f"   最大深度: {crawler.max_depth}")
    print(f"   最大请求数: {crawler.max_requests}")
    print(f"   每秒请求数: {crawler.requests_per_second}")
    print(f"   User-Agent 数量: {len(crawler.user_agents)}")
    
    # 演示 User-Agent 轮换
    print(f"\n🔄 User-Agent 轮换演示:")
    for i in range(3):
        ua = crawler.get_random_user_agent()
        browser_info = ua.split(')')[0] + ')'
        print(f"   {i+1}. {browser_info}")
    
    # 爬取示例网站
    print(f"\n🌐 开始爬取示例网站...")
    test_url = "https://httpbin.org/html"
    
    results = crawler.crawl(test_url)
    
    print(f"\n📊 爬取结果:")
    print(f"   成功爬取: {len(results)} 个页面")
    
    for url, content in results.items():
        print(f"\n   📄 {url}")
        print(f"      内容长度: {len(content)} 字符")
        # 显示内容的前150个字符
        preview = content.replace('\n', ' ').strip()[:150]
        print(f"      内容预览: {preview}...")
    
    # 保存结果
    output_dir = "demo_output"
    crawler.save_to_files(output_dir)
    print(f"\n💾 结果已保存到 '{output_dir}' 目录")
    
    print(f"\n✅ 演示完成！")

if __name__ == "__main__":
    demo_crawler()
