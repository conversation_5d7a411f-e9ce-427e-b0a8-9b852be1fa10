"""
快速测试网页爬取和关键词提取功能
"""

from model import GeoExpert
from config import config
from web_crawler import SafeWebCrawler

def quick_test():
    print("🚀 快速功能测试")
    print("=" * 40)
    
    # 使用可靠的测试网站
    url = "https://example.com"
    
    try:
        # 创建实例
        crawler = SafeWebCrawler(config)
        geo_expert = GeoExpert(config)
        
        print(f"🕷️ 爬取网站: {url}")
        
        # 爬取
        crawled_text = crawler.crawl(url)
        
        if crawled_text:
            # 合并文本
            combined_text = ""
            for url_key, content in crawled_text.items():
                combined_text += content + "\n"
            
            print(f"✅ 爬取成功: {len(crawled_text)} 页面, {len(combined_text)} 字符")
            
            # 分析关键词
            print("🤖 分析关键词...")
            keywords = geo_expert.keyword_extract(combined_text)
            
            print("📊 结果:")
            print(keywords)
            
        else:
            print("❌ 没有爬取到内容")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    quick_test()
